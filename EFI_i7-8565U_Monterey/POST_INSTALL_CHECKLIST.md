# Post-Installation Checklist for Intel i7-8565U Whiskey <PERSON> Hackintosh

## Phase 1: Essential System Verification (Day 1)

### ✅ Boot and Basic Functionality
- [ ] System boots from internal drive without USB
- [ ] OpenCore menu appears and functions correctly
- [ ] macOS loads completely to desktop
- [ ] System Information shows correct hardware details
- [ ] About This Mac displays proper model (MacBookPro15,2)

### ✅ Graphics Verification
- [ ] Display resolution is correct (1920x1080)
- [ ] Graphics acceleration is working (check About This Mac → Graphics)
- [ ] Brightness controls work (F1/F2 or Fn+F1/F2)
- [ ] External display detection (if applicable)
- [ ] No graphical glitches or artifacts

### ✅ Audio Testing
- [ ] Internal speakers produce sound
- [ ] Headphone jack works
- [ ] Microphone input functions (if needed)
- [ ] Volume controls work
- [ ] Audio persists after sleep/wake

### ✅ Input Devices
- [ ] Keyboard functions properly
- [ ] All keys respond correctly
- [ ] Function keys work (brightness, volume, etc.)
- [ ] Trackpad responds to touch
- [ ] Trackpad gestures work (scroll, tap, etc.)
- [ ] External USB mouse/keyboard work

### ✅ Network Connectivity
- [ ] WiFi networks are detected and connect
- [ ] Internet browsing works
- [ ] WiFi speed is acceptable
- [ ] Bluetooth devices can be paired
- [ ] Bluetooth audio works (if applicable)

## Phase 2: USB and Power Management (Day 2-3)

### ✅ USB Port Mapping
- [ ] All external USB ports function
- [ ] USB 2.0 devices work at correct speed
- [ ] USB 3.0 devices work at correct speed
- [ ] USB-C ports function (if applicable)
- [ ] Internal USB devices work (webcam, Bluetooth)
- [ ] UTBMap.kext is generated and enabled
- [ ] XhciPortLimit quirk is disabled

### ✅ Sleep and Wake Testing
- [ ] System enters sleep mode properly
- [ ] System wakes from sleep reliably
- [ ] All devices work after wake
- [ ] No kernel panics during sleep/wake
- [ ] Battery percentage is accurate after wake

### ✅ Power Management
- [ ] CPU frequency scaling works (check with Intel Power Gadget)
- [ ] Battery life is reasonable
- [ ] Charging works properly
- [ ] Power adapter is recognized
- [ ] Thermal management is functioning

## Phase 3: Advanced Features (Week 1)

### ✅ iCloud and Apple Services
- [ ] Apple ID sign-in works
- [ ] iCloud sync functions
- [ ] App Store access
- [ ] iMessage works (if desired)
- [ ] FaceTime works (if desired)

### ✅ Continuity Features (with BCM94360NG)
- [ ] AirDrop functions between devices
- [ ] Handoff works with iPhone/iPad
- [ ] Universal Clipboard functions
- [ ] Instant Hotspot works

### ✅ System Features
- [ ] Time Machine backup works
- [ ] FileVault encryption (test carefully)
- [ ] System updates check (don't install yet)
- [ ] Spotlight indexing completes
- [ ] Notification Center functions

### ✅ Performance Verification
- [ ] System feels responsive
- [ ] No excessive fan noise
- [ ] Temperatures are reasonable
- [ ] No memory leaks or high CPU usage
- [ ] Storage performance is good

## Phase 4: Stability Testing (Week 2)

### ✅ Extended Testing
- [ ] System runs for 24+ hours without issues
- [ ] Multiple sleep/wake cycles work
- [ ] Heavy workload testing (video, compilation, etc.)
- [ ] Multiple app usage simultaneously
- [ ] External device connections/disconnections

### ✅ Edge Case Testing
- [ ] Low battery scenarios
- [ ] High CPU load situations
- [ ] Multiple external displays (if applicable)
- [ ] USB device hotplugging
- [ ] Network switching (WiFi to Ethernet)

### ✅ Error Monitoring
- [ ] Check Console.app for errors
- [ ] Monitor kernel panics
- [ ] Watch for USB errors
- [ ] Check graphics driver issues
- [ ] Monitor thermal throttling

## Phase 5: Optimization and Maintenance (Ongoing)

### ✅ Performance Optimization
- [ ] Disable unnecessary startup items
- [ ] Optimize energy settings
- [ ] Clean up temporary files
- [ ] Monitor storage usage
- [ ] Check for memory leaks

### ✅ Security Configuration
- [ ] Enable firewall
- [ ] Configure privacy settings
- [ ] Set up secure passwords
- [ ] Enable automatic lock
- [ ] Configure backup encryption

### ✅ Backup Strategy
- [ ] Create complete EFI backup
- [ ] Set up Time Machine
- [ ] Document configuration changes
- [ ] Create recovery USB
- [ ] Test restore procedures

## Troubleshooting Checklist

### ✅ If Issues Arise
- [ ] Check TROUBLESHOOTING.md for solutions
- [ ] Verify all file paths in config.plist
- [ ] Ensure kext loading order is correct
- [ ] Check for conflicting settings
- [ ] Review OpenCore logs

### ✅ Before Seeking Help
- [ ] Try basic troubleshooting steps
- [ ] Search existing forums/guides
- [ ] Prepare detailed problem description
- [ ] Gather system information
- [ ] Create minimal test configuration

## Maintenance Schedule

### Weekly Tasks
- [ ] Check system logs for errors
- [ ] Monitor performance metrics
- [ ] Verify backup integrity
- [ ] Check for unusual behavior

### Monthly Tasks
- [ ] Review and update documentation
- [ ] Check for kext updates (research first)
- [ ] Clean system caches
- [ ] Verify EFI backup currency

### Quarterly Tasks
- [ ] Full system backup
- [ ] Review security settings
- [ ] Check for OpenCore updates
- [ ] Performance benchmarking

## Documentation Tasks

### ✅ Configuration Documentation
- [ ] Document working audio layout-id
- [ ] Record USB port mapping
- [ ] Note any custom patches
- [ ] Document BIOS settings
- [ ] Record working kext versions

### ✅ Issue Tracking
- [ ] Document any problems encountered
- [ ] Record solutions that worked
- [ ] Note hardware-specific quirks
- [ ] Track performance baselines

## Success Criteria

### Minimum Acceptable Performance
- [ ] System boots reliably (>95% success rate)
- [ ] Sleep/wake works consistently
- [ ] All essential hardware functions
- [ ] No data corruption or loss
- [ ] Acceptable performance for daily use

### Optimal Performance Goals
- [ ] Native-like user experience
- [ ] All hardware features working
- [ ] Excellent battery life
- [ ] Silent operation under normal load
- [ ] Full macOS feature compatibility

## Red Flags - Stop and Fix

### Critical Issues
- [ ] Frequent kernel panics
- [ ] Data corruption
- [ ] Overheating issues
- [ ] Battery not charging
- [ ] System instability

### Warning Signs
- [ ] Excessive fan noise
- [ ] Poor battery life
- [ ] Slow performance
- [ ] USB devices disconnecting
- [ ] Graphics glitches

## Final Validation

### ✅ Ready for Daily Use When:
- [ ] All Phase 1-4 items completed
- [ ] No critical issues present
- [ ] Backup strategy implemented
- [ ] Documentation complete
- [ ] Comfortable with troubleshooting

### ✅ System Hardening
- [ ] Remove verbose boot arguments
- [ ] Disable debug logging
- [ ] Set secure boot options
- [ ] Configure automatic updates (carefully)
- [ ] Implement monitoring tools

## Notes Section

### Hardware-Specific Notes
```
Record any laptop-specific findings here:
- Audio codec and working layout-id
- Trackpad type and required kexts
- USB port mapping details
- Any custom patches needed
```

### Performance Baselines
```
Record baseline performance metrics:
- Boot time: _____ seconds
- Sleep/wake time: _____ seconds
- Battery life: _____ hours
- Geekbench scores: CPU _____ GPU _____
```

### Known Issues
```
Document any unresolved issues:
- Issue description
- Workaround (if any)
- Impact on daily use
- Potential solutions to try
```

**Completion Date**: ___________
**System Stable**: Yes / No
**Ready for Production Use**: Yes / No
**Next Review Date**: ___________
