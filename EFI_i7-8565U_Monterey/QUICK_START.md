# Quick Start Guide - Intel i7-8565U Whiskey <PERSON> Hackintosh

## ⚡ Fast Track Setup (For Experienced Users)

### Prerequisites Checklist
- [ ] Intel i7-8565U laptop with Intel UHD 620
- [ ] BCM94360NG WiFi card installed
- [ ] 16GB USB drive
- [ ] Windows/macOS computer for preparation
- [ ] Latest BIOS installed

### 1. Download Everything (30 minutes)
```bash
# Essential Downloads:
- OpenCore 1.0.5
- macOS Monterey installer
- All kexts from DOWNLOAD_LINKS.md
- GenSMBIOS tool
- USBToolBox tool
```

### 2. BIOS Configuration (10 minutes)
**Critical Settings:**
- Secure Boot: **DISABLED**
- VT-d: **DISABLED** 
- CFG Lock: **DISABLED** (if available)
- SATA Mode: **AHCI**
- XHCI Hand-off: **ENABLED**

### 3. Create EFI (20 minutes)
1. **Copy provided config.plist** to EFI/OC/
2. **Generate SMBIOS** with GenSMBIOS (MacBookPro15,2)
3. **Update config.plist** with your serials
4. **Copy all kexts** to EFI/OC/Kexts/
5. **Copy ACPI files** to EFI/OC/ACPI/

### 4. Install macOS (2 hours)
1. **Create USB installer** with EFI
2. **Boot from USB** and install
3. **Copy EFI** to internal drive after installation

### 5. Post-Install (1 hour)
1. **Map USB ports** with USBToolBox
2. **Test audio** with different layout-ids
3. **Verify sleep/wake** functionality

## 🔧 Key Configuration Points

### Graphics (Intel UHD 620)
```xml
<key>PciRoot(0x0)/Pci(0x2,0x0)</key>
<dict>
    <key>AAPL,ig-platform-id</key>
    <data>CQDqAw==</data>  <!-- 0x3EA50009 -->
    <key>device-id</key>
    <data>mz4AAA==</data>   <!-- 0x9B3E0000 -->
</dict>
```

### Audio (Test These Layout-IDs)
- **ALC256**: 13, 21, 56, 97
- **ALC295**: 3, 28, 99  
- **ALC225**: 13, 28, 33

### SMBIOS
- **Model**: MacBookPro15,2
- **Generate unique serials** with GenSMBIOS

## 🚨 Common Issues & Quick Fixes

### Boot Issues
- **Black screen**: Try platform-id `0x3EA50000`
- **Kernel panic**: Enable CFG Lock quirks if BIOS locked
- **Stuck at logo**: Add `-v` boot argument for verbose

### No Audio
```xml
<!-- Try different layout-id values -->
<key>layout-id</key>
<data>AQAAAA==</data>  <!-- layout-id 1 -->
```

### Trackpad Not Working
- **PS/2**: Use VoodooPS2Controller.kext
- **I2C**: Use VoodooI2C.kext + VoodooI2CHID.kext

### Sleep Issues
1. **Complete USB mapping** first
2. **Disable XhciPortLimit** after mapping
3. **Check wake reasons** with `pmset -g log`

## 📁 File Structure
```
EFI/
├── BOOT/
│   └── BOOTx64.efi
└── OC/
    ├── ACPI/
    │   ├── SSDT-PLUG.aml
    │   ├── SSDT-EC-USBX.aml
    │   └── SSDT-PNLF.aml
    ├── Drivers/
    │   ├── OpenRuntime.efi
    │   └── HfsPlus.efi
    ├── Kexts/
    │   ├── Lilu.kext
    │   ├── VirtualSMC.kext
    │   ├── WhateverGreen.kext
    │   ├── AppleALC.kext
    │   ├── VoodooPS2Controller.kext
    │   ├── NVMeFix.kext
    │   └── USBToolBox.kext
    ├── OpenCore.efi
    └── config.plist
```

## ⚙️ Essential Boot Arguments
```
# Initial installation:
-v keepsyms=1 debug=0x100 alcid=1

# After stable:
alcid=X (replace X with working layout-id)
```

## 🔄 USB Mapping Quick Steps
1. **Install with XhciPortLimit=true**
2. **Run USBToolBox tool** in Windows
3. **Test all USB ports** with devices
4. **Generate UTBMap.kext**
5. **Enable UTBMap.kext** in config.plist
6. **Disable XhciPortLimit**

## 📋 Verification Checklist
- [ ] System boots from internal drive
- [ ] Graphics acceleration working
- [ ] Audio output functional
- [ ] WiFi/Bluetooth working (BCM94360NG)
- [ ] Keyboard/trackpad responsive
- [ ] USB ports functional
- [ ] Sleep/wake working
- [ ] Battery monitoring accurate

## 🆘 Emergency Recovery
If system won't boot:
1. **Boot from USB installer**
2. **Replace EFI** with backup
3. **Reset BIOS** to defaults
4. **Check file permissions**

## 📚 Full Documentation
For detailed instructions, see:
- **INSTALLATION_GUIDE.md** - Complete setup process
- **USB_MAPPING_GUIDE.md** - Detailed USB mapping
- **TROUBLESHOOTING.md** - Problem solutions
- **BIOS_SETUP.md** - BIOS configuration details

## ⚡ Performance Tips
- **Enable HiDPI** for better display scaling
- **Optimize energy settings** for battery life
- **Monitor temperatures** with HWiNFO
- **Use SSD TRIM** for storage optimization

## 🔒 Security Notes
- **Generate unique SMBIOS** - don't use defaults
- **Enable FileVault** after confirming stability
- **Keep EFI backups** secure
- **Use strong passwords**

## 🎯 Success Criteria
Your hackintosh is ready when:
- ✅ Boots reliably without USB
- ✅ All hardware functions properly
- ✅ Sleep/wake works consistently
- ✅ Performance is acceptable
- ✅ No critical errors in logs

## 📞 Getting Help
If you need assistance:
1. **Check TROUBLESHOOTING.md** first
2. **Search existing forums** for your issue
3. **Provide complete information** when asking
4. **Include EFI folder** and error details

**Estimated Total Time**: 4-6 hours for complete setup
**Difficulty Level**: Intermediate to Advanced
**Success Rate**: High with proper preparation

Good luck with your hackintosh build! 🍎
