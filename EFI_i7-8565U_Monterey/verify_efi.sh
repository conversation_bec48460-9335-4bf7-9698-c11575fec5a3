#!/bin/bash

# EFI Verification Script for Intel i7-8565U Whiskey Lake Hackintosh
# This script verifies that all required files are present in the EFI structure

echo "🔍 EFI Structure Verification Script"
echo "===================================="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Counters
total_files=0
missing_files=0
present_files=0

# Function to check if file exists
check_file() {
    local file_path=$1
    local description=$2
    
    total_files=$((total_files + 1))
    
    if [ -f "$file_path" ]; then
        echo -e "✅ ${GREEN}FOUND${NC}: $file_path ($description)"
        present_files=$((present_files + 1))
    else
        echo -e "❌ ${RED}MISSING${NC}: $file_path ($description)"
        missing_files=$((missing_files + 1))
    fi
}

# Function to check if directory exists
check_directory() {
    local dir_path=$1
    local description=$2
    
    if [ -d "$dir_path" ]; then
        echo -e "📁 ${GREEN}FOUND${NC}: $dir_path/ ($description)"
    else
        echo -e "📁 ${RED}MISSING${NC}: $dir_path/ ($description)"
    fi
}

echo "🔍 Checking EFI directory structure..."
echo ""

# Check main directories
check_directory "EFI" "Main EFI directory"
check_directory "EFI/BOOT" "Boot directory"
check_directory "EFI/OC" "OpenCore directory"
check_directory "EFI/OC/ACPI" "ACPI directory"
check_directory "EFI/OC/Drivers" "Drivers directory"
check_directory "EFI/OC/Kexts" "Kexts directory"
check_directory "EFI/OC/Tools" "Tools directory"

echo ""
echo "🔍 Checking required files..."
echo ""

# Check BOOT files
echo "📂 BOOT Files:"
check_file "EFI/BOOT/BOOTx64.efi" "UEFI bootloader"

echo ""
echo "📂 OpenCore Files:"
check_file "EFI/OC/OpenCore.efi" "OpenCore bootloader"
check_file "EFI/OC/config.plist" "OpenCore configuration"

echo ""
echo "📂 Driver Files:"
check_file "EFI/OC/Drivers/OpenRuntime.efi" "OpenCore runtime driver"
check_file "EFI/OC/Drivers/HfsPlus.efi" "HFS+ filesystem driver"

echo ""
echo "📂 ACPI Files:"
check_file "EFI/OC/ACPI/SSDT-PLUG.aml" "CPU power management"
check_file "EFI/OC/ACPI/SSDT-EC-USBX.aml" "Embedded controller and USB power"
check_file "EFI/OC/ACPI/SSDT-PNLF.aml" "Backlight control"

echo ""
echo "📂 Kext Files:"
check_file "EFI/OC/Kexts/Lilu.kext/Contents/Info.plist" "Lilu patching framework"
check_file "EFI/OC/Kexts/VirtualSMC.kext/Contents/Info.plist" "Virtual SMC"
check_file "EFI/OC/Kexts/SMCProcessor.kext/Contents/Info.plist" "SMC Processor monitoring"
check_file "EFI/OC/Kexts/SMCSuperIO.kext/Contents/Info.plist" "SMC SuperIO monitoring"
check_file "EFI/OC/Kexts/SMCBatteryManager.kext/Contents/Info.plist" "SMC Battery monitoring"
check_file "EFI/OC/Kexts/WhateverGreen.kext/Contents/Info.plist" "Graphics acceleration"
check_file "EFI/OC/Kexts/AppleALC.kext/Contents/Info.plist" "Audio codec support"
check_file "EFI/OC/Kexts/VoodooPS2Controller.kext/Contents/Info.plist" "PS/2 input devices"
check_file "EFI/OC/Kexts/NVMeFix.kext/Contents/Info.plist" "NVMe SSD optimization"
check_file "EFI/OC/Kexts/USBToolBox.kext/Contents/Info.plist" "USB mapping framework"

echo ""
echo "📂 Optional Tool Files:"
if [ -f "EFI/OC/Tools/ResetNVRAM.efi" ]; then
    echo -e "✅ ${GREEN}FOUND${NC}: EFI/OC/Tools/ResetNVRAM.efi (NVRAM reset tool)"
else
    echo -e "⚠️  ${YELLOW}OPTIONAL${NC}: EFI/OC/Tools/ResetNVRAM.efi (NVRAM reset tool)"
fi

echo ""
echo "🔍 Checking config.plist content..."

# Check if config.plist contains required sections
if [ -f "EFI/OC/config.plist" ]; then
    if grep -q "MacBookPro15,2" "EFI/OC/config.plist"; then
        echo -e "✅ ${GREEN}FOUND${NC}: MacBookPro15,2 SMBIOS in config.plist"
    else
        echo -e "❌ ${RED}MISSING${NC}: MacBookPro15,2 SMBIOS in config.plist"
    fi
    
    if grep -q "CHANGEME" "EFI/OC/config.plist"; then
        echo -e "⚠️  ${YELLOW}WARNING${NC}: config.plist contains placeholder values (CHANGEME)"
        echo "   You must generate unique SMBIOS values with GenSMBIOS!"
    else
        echo -e "✅ ${GREEN}GOOD${NC}: No placeholder values found in config.plist"
    fi
    
    if grep -q "0x3EA50009" "EFI/OC/config.plist"; then
        echo -e "✅ ${GREEN}FOUND${NC}: Intel UHD 620 graphics configuration"
    else
        echo -e "❌ ${RED}MISSING${NC}: Intel UHD 620 graphics configuration"
    fi
fi

echo ""
echo "📊 Verification Summary:"
echo "======================="
echo -e "Total files checked: $total_files"
echo -e "Files present: ${GREEN}$present_files${NC}"
echo -e "Files missing: ${RED}$missing_files${NC}"

echo ""
if [ $missing_files -eq 0 ]; then
    echo -e "🎉 ${GREEN}SUCCESS${NC}: All required files are present!"
    echo ""
    echo "📋 Next steps:"
    echo "1. Generate unique SMBIOS values with GenSMBIOS (MacBookPro15,2)"
    echo "2. Update config.plist with your generated serials"
    echo "3. Copy EFI folder to your USB installer"
    echo "4. Follow INSTALLATION_GUIDE.md for installation"
else
    echo -e "❌ ${RED}INCOMPLETE${NC}: $missing_files files are missing!"
    echo ""
    echo "📋 To fix missing files:"
    echo "1. Run download_files.sh (Linux/macOS) or download_files.bat (Windows)"
    echo "2. Or manually download missing files following DOWNLOAD_AND_SETUP.md"
    echo "3. Run this verification script again"
fi

echo ""
echo -e "⚠️  ${YELLOW}IMPORTANT REMINDERS${NC}:"
echo "• Generate unique SMBIOS values - never use defaults!"
echo "• Update BIOS settings according to BIOS_SETUP.md"
echo "• Create USB installer with proper macOS Monterey image"
echo "• Follow installation guide step by step"
echo ""
