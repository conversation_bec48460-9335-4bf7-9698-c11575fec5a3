# Troubleshooting Guide for Intel i7-8565U Whiskey <PERSON> Hackintosh

## Boot Issues

### Kernel Panic at Boot
**Symptoms**: System crashes with kernel panic during boot

**Common Causes & Solutions**:
1. **Incompatible kexts**:
   - Disable all non-essential kexts
   - Add kexts one by one to identify problematic one
   - Ensure kext versions are compatible with macOS Monterey

2. **ACPI issues**:
   - Remove custom SSDT files temporarily
   - Check ACPI patches in config.plist
   - Verify SSDT-PLUG-DRTNIA is for mobile processors

3. **CFG Lock enabled**:
   - Enable `AppleCpuPmCfgLock` and `AppleXcpmCfgLock` quirks
   - Try to disable CFG Lock in BIOS if available

### Black Screen After Apple Logo
**Symptoms**: System boots but screen goes black after Apple logo

**Solutions**:
1. **Graphics configuration**:
   - Try alternative platform-id: `0x3EA50000`
   - Verify device-id is set to `0x9B3E0000`
   - Add `-igfxvesa` boot argument for safe graphics mode

2. **Display connection**:
   - Try external monitor if available
   - Check BIOS graphics settings
   - Verify DVMT allocation in BIOS

### Stuck at Apple Logo
**Symptoms**: Boot process stops at Apple logo

**Solutions**:
1. **Enable verbose mode**:
   - Add `-v` to boot arguments
   - Identify where boot process stops

2. **Common fixes**:
   - Verify all required kexts are present
   - Check ACPI patches
   - Ensure proper SMBIOS configuration

### Boot Loop
**Symptoms**: System continuously reboots

**Solutions**:
1. **Reset NVRAM**:
   - Add `ResetNVRAM.efi` to Tools folder
   - Boot to OpenCore menu and select Reset NVRAM

2. **Check configuration**:
   - Verify config.plist syntax
   - Ensure all file paths are correct
   - Check for conflicting settings

## Graphics Issues

### No Graphics Acceleration
**Symptoms**: Slow graphics, low resolution, no hardware acceleration

**Solutions**:
1. **Verify graphics properties**:
   ```xml
   <key>PciRoot(0x0)/Pci(0x2,0x0)</key>
   <dict>
       <key>AAPL,ig-platform-id</key>
       <data>CQDqAw==</data>
       <key>device-id</key>
       <data>mz4AAA==</data>
   </dict>
   ```

2. **Alternative configurations**:
   - Platform-ID: `0x3EA50000` (headless)
   - Platform-ID: `0x3EA50009` (with outputs)
   - Add framebuffer patches if needed

### External Display Not Working
**Symptoms**: Laptop display works but external monitor doesn't

**Solutions**:
1. **Check platform-id**: Use `0x3EA50009` for display outputs
2. **Add connector patches**: May need custom framebuffer patches
3. **Test different cables**: Try different connection types (HDMI, USB-C)

### Brightness Control Not Working
**Symptoms**: Cannot adjust screen brightness

**Solutions**:
1. **Verify SSDT-PNLF**: Ensure it's loaded and enabled
2. **Add brightness kext**: Try BrightnessKeys.kext
3. **Check ACPI patches**: May need additional backlight patches

## Audio Issues

### No Audio Output
**Symptoms**: No sound from speakers or headphones

**Solutions**:
1. **Try different layout-ids**:
   - Common for laptops: 1, 3, 13, 21, 28, 56, 97, 99
   - Update in DeviceProperties → PciRoot(0x0)/Pci(0x1f,0x3) → layout-id

2. **Identify audio codec**:
   - Use Linux live USB to identify codec
   - Check laptop specifications
   - Search for laptop-specific audio solutions

3. **Verify AppleALC**:
   - Ensure AppleALC.kext is loaded
   - Check for conflicting audio kexts
   - Try different AppleALC versions

### Microphone Not Working
**Symptoms**: Audio output works but microphone doesn't

**Solutions**:
1. **Test different layout-ids**: Some layouts support different I/O configurations
2. **Check privacy settings**: Ensure microphone access is enabled
3. **Hardware verification**: Test microphone in Windows/Linux

## Input Device Issues

### Trackpad Not Working
**Symptoms**: Trackpad not responsive or partially working

**Solutions**:
1. **Identify trackpad type**:
   - PS/2: Use VoodooPS2Controller.kext
   - I2C: Use VoodooI2C.kext + VoodooI2CHID.kext
   - Check Device Manager in Windows

2. **VoodooI2C setup** (if I2C trackpad):
   - Add VoodooI2C.kext and VoodooI2CHID.kext
   - May need GPIO pinning patches
   - Disable VoodooPS2Trackpad plugin

3. **ACPI patches**: May need SSDT-XOSI.aml for trackpad compatibility

### Keyboard Issues
**Symptoms**: Some keys not working or wrong key mapping

**Solutions**:
1. **VoodooPS2 configuration**: Check VoodooPS2Controller settings
2. **Key remapping**: Use Karabiner-Elements for custom key mapping
3. **Function keys**: May need additional ACPI patches

## WiFi and Bluetooth Issues

### WiFi Not Working
**Symptoms**: No WiFi networks detected

**Solutions**:
1. **Verify BCM94360NG installation**:
   - Check physical connection
   - Verify antenna connections
   - Ensure card is properly seated

2. **Check System Information**:
   - WiFi should show as "Broadcom" in System Information
   - If not detected, check hardware installation

### Bluetooth Not Working
**Symptoms**: Bluetooth not available or devices won't pair

**Solutions**:
1. **USB mapping**: Ensure Bluetooth USB port is mapped
2. **Reset Bluetooth module**: Delete Bluetooth preferences
3. **Check for conflicts**: Ensure no conflicting Bluetooth kexts

## Sleep and Wake Issues

### System Won't Sleep
**Symptoms**: Laptop doesn't enter sleep mode

**Solutions**:
1. **Complete USB mapping**: Essential for proper sleep
2. **Check wake reasons**: Use `pmset -g log` to identify wake causes
3. **Disable problematic devices**: May need to disable certain USB devices

### System Won't Wake
**Symptoms**: System sleeps but won't wake up

**Solutions**:
1. **USB mapping**: Verify all USB ports are properly mapped
2. **Graphics configuration**: May need additional framebuffer patches
3. **Power management**: Check ACPI power management patches

### Random Wake Events
**Symptoms**: System wakes up unexpectedly

**Solutions**:
1. **Disable wake sources**: Use `pmset` to disable network wake, etc.
2. **Check USB devices**: Some USB devices may cause wake events
3. **BIOS settings**: Disable wake on LAN, wake on USB

## Performance Issues

### Poor Battery Life
**Symptoms**: Battery drains quickly

**Solutions**:
1. **Power management**: Verify SSDT-PLUG-DRTNIA is working
2. **CPU frequency scaling**: Check with Intel Power Gadget
3. **Background processes**: Monitor for high CPU usage apps

### Thermal Throttling
**Symptoms**: System gets hot and performance drops

**Solutions**:
1. **Monitor temperatures**: Use HWiNFO or similar tools
2. **Check thermal paste**: May need laptop servicing
3. **Power management**: Ensure proper CPU power management

### Slow Performance
**Symptoms**: Overall system sluggishness

**Solutions**:
1. **Graphics acceleration**: Verify hardware acceleration is working
2. **Storage performance**: Check SSD health and TRIM support
3. **Memory usage**: Monitor RAM usage and swap

## Network Issues

### Ethernet Not Working
**Symptoms**: Wired network connection not available

**Solutions**:
1. **Identify ethernet controller**: Check hardware specifications
2. **Add appropriate kext**:
   - Intel: IntelMausi.kext
   - Realtek: RealtekRTL8111.kext
3. **Check BIOS settings**: Ensure ethernet is enabled

## Installation Issues

### Installer Won't Boot
**Symptoms**: macOS installer doesn't start

**Solutions**:
1. **Verify installer integrity**: Re-download macOS installer
2. **Check USB creation**: Ensure proper USB installer creation
3. **BIOS settings**: Verify all required BIOS settings

### Installation Fails
**Symptoms**: Installation starts but fails partway through

**Solutions**:
1. **Storage configuration**: Ensure SATA mode is AHCI
2. **Disk format**: Use APFS for macOS Monterey
3. **Free space**: Ensure adequate free space (60GB+)

## Emergency Recovery

### System Won't Boot After Changes
**Solutions**:
1. **Boot from USB installer**: Use original USB installer
2. **Restore EFI backup**: Replace EFI with known working version
3. **Reset BIOS**: Clear CMOS if necessary

### Corrupted EFI
**Solutions**:
1. **Recreate EFI**: Use backup or rebuild from scratch
2. **Check file permissions**: Ensure proper EFI file permissions
3. **Verify file integrity**: Check for corrupted files

## Diagnostic Tools

### Essential Tools
- **IORegistryExplorer**: Hardware analysis
- **Hackintool**: System information and patches
- **Intel Power Gadget**: CPU monitoring
- **Console.app**: System logs
- **Activity Monitor**: Process monitoring

### Log Analysis
- **OpenCore logs**: Check for boot errors
- **System logs**: Use Console.app for system errors
- **Kernel logs**: Check for kernel panics

### Hardware Testing
- **Apple Hardware Test**: Boot with D key
- **Memory test**: Use MemTest86
- **Storage test**: Use disk utility first aid

## Getting Help

### Information to Provide
When seeking help, include:
- Exact laptop model
- macOS version
- OpenCore version
- Complete EFI folder
- Detailed problem description
- Steps already tried

### Useful Communities
- **r/hackintosh**: Reddit community
- **tonymacx86**: Forums
- **InsanelyMac**: Forums
- **Dortania Discord**: Real-time help

### Before Asking for Help
1. **Search existing solutions**: Check forums and guides
2. **Try basic troubleshooting**: Follow this guide first
3. **Provide complete information**: Include all relevant details
4. **Be patient**: Community help is volunteer-based

Remember: Every laptop is unique, and solutions may need customization for your specific hardware configuration.
