# USB Mapping Guide for Intel i7-8565U Whiskey Lake Laptops

## Overview
USB mapping is crucial for proper sleep/wake functionality and USB port recognition in macOS. Laptops typically have fewer USB ports than desktops but may still exceed macOS's 15-port limit when including internal devices.

## Prerequisites
- macOS Monterey successfully installed and booting
- EFI configured with USBToolBox.kext enabled
- XhciPortLimit=true in config.plist (temporary for mapping)
- Access to Windows (recommended) or macOS for USBToolBox tool

## Understanding Laptop USB Architecture

### Common Laptop USB Controllers
- **XHCI Controller**: Usually `PciRoot(0x0)/Pci(0x14,0x0)` on Intel platforms
- **Internal Devices**: Bluetooth, webcam, card readers often use internal USB ports
- **External Ports**: Physical USB-A and USB-C ports accessible to users

### Typical Laptop USB Port Layout
**External Ports** (varies by laptop model):
- **USB-A Ports**: 2-4 ports (USB 2.0 and/or USB 3.0)
- **USB-C Ports**: 1-2 ports (may support Thunderbolt)

**Internal Devices**:
- **Bluetooth Controller**: Usually on internal USB 2.0 port
- **Webcam**: Internal USB 2.0 port
- **Card Reader**: Internal USB 2.0 port (if present)
- **Fingerprint Reader**: Internal USB 2.0 port (if present)

## Method 1: USBToolBox Tool (Recommended)

### Step 1: Download USBToolBox Tool
1. Download from: https://github.com/USBToolBox/tool/releases
2. Choose the appropriate version:
   - **Windows**: `Windows.exe` (recommended for easier identification)
   - **macOS**: `macOS` (run in Terminal)

### Step 2: Run USBToolBox Tool

#### On Windows (Recommended):
```cmd
Windows.exe
```

#### On macOS:
```bash
chmod +x macOS
./macOS
```

### Step 3: Discover Ports
1. Select option `D` - Discover Ports
2. **Test each physical USB port** on your laptop:
   - Insert a USB device (flash drive, mouse, keyboard)
   - Note which port number appears in the tool
   - Test both USB 2.0 and USB 3.0 devices in each port
   - Document the port layout for your specific laptop

### Step 4: Identify Internal Devices
**Important**: Some internal devices may not show up during discovery.

Common internal USB devices:
- **Bluetooth**: Look for "Bluetooth" or "BCM" in device names
- **Webcam**: Look for "Camera" or "USB Video" devices
- **Card Reader**: Look for "Card Reader" or "Realtek" devices

### Step 5: Select Ports (15 Port Limit)

#### Recommended Port Selection Strategy:
1. **Keep all external USB ports** (highest priority)
2. **Keep Bluetooth controller** (if using internal Bluetooth)
3. **Keep webcam** (if you use it)
4. **Keep card reader** (if you use it)
5. **Remove unused internal devices** to stay under 15 ports

#### Port Types to Assign:
- **USB 2.0 ports**: Type 0 (USB2)
- **USB 3.0 ports**: Type 3 (USB3)
- **USB-C ports**: Type 9 (TypeC+Sw) or Type 10 (TypeC)
- **Internal devices**: Usually Type 0 (USB2)

### Step 6: Generate UTBMap.kext
1. Select option `K` - Build UTBMap.kext
2. The tool will generate `UTBMap.kext`
3. Copy this file to your EFI: `EFI/OC/Kexts/UTBMap.kext`

### Step 7: Update config.plist
1. **Enable UTBMap.kext**:
   - In config.plist → Kernel → Add
   - Find UTBMap.kext entry
   - Set `Enabled` to `true`

2. **Disable XhciPortLimit**:
   - In config.plist → Kernel → Quirks
   - Set `XhciPortLimit` to `false`

3. **Save config.plist** and reboot

## Method 2: Manual Identification (Advanced)

### Step 1: Use IORegistryExplorer
1. Download IORegistryExplorer from Apple Developer
2. Look for USB controllers under IOService
3. Identify XHCI controller (usually XHC or XHCI)

### Step 2: Map Physical Ports
1. Use USB devices to test each physical port
2. Note the port numbers in IORegistryExplorer
3. Create a mapping document for your laptop

## Laptop-Specific Considerations

### Power Management
- Laptops have stricter USB power management
- Some ports may be disabled during sleep
- Test sleep/wake after USB mapping

### Thunderbolt/USB-C
- Thunderbolt ports may require additional configuration
- USB-C ports can be complex (data + power + video)
- Test with different USB-C devices

### Internal Device Conflicts
- Some internal devices may conflict with external ports
- Bluetooth may stop working if its USB port is disabled
- Webcam functionality depends on proper USB mapping

## Verification and Testing

### Test USB Functionality
After applying USB mapping:
1. **Test all external ports** with different devices
2. **Verify internal devices** (Bluetooth, webcam)
3. **Test sleep/wake** functionality
4. **Check USB 3.0 speeds** with USB 3.0 devices
5. **Test USB-C functionality** if applicable

### Performance Testing
- **USB 2.0 devices**: Should show 480 Mbps max speed
- **USB 3.0 devices**: Should show 5 Gbps max speed
- **USB-C devices**: Test data transfer and charging

## Troubleshooting

### Common Issues
- **Ports not working**: Check port numbers in USBToolBox
- **Sleep issues persist**: Verify XhciPortLimit=false
- **USB 3.0 slow**: Check port types (should be Type 3)
- **Bluetooth not working**: Ensure Bluetooth USB port is mapped
- **Webcam not working**: Ensure webcam USB port is mapped

### Debug Steps
1. **Check OpenCore logs** for USB-related errors
2. **Use IORegistryExplorer** to verify port mapping
3. **Test with minimal configuration** (only essential ports)
4. **Re-run USBToolBox** if issues persist

## Laptop Model Variations

### Different Manufacturers
- **Dell**: Often has consistent USB layouts within series
- **HP**: May have unique internal device configurations
- **Lenovo**: ThinkPads often have additional internal devices
- **ASUS**: Gaming laptops may have more USB ports

### Research Your Model
1. **Search for laptop-specific guides** online
2. **Check hackintosh forums** for your exact model
3. **Document your findings** for future reference

## Final Steps
1. **Backup working EFI** after successful USB mapping
2. **Document your port layout** for future reference
3. **Test thoroughly** before considering complete
4. **Share your findings** with the hackintosh community

## Additional Resources
- **USBToolBox Documentation**: https://github.com/USBToolBox/tool
- **Dortania USB Guide**: https://dortania.github.io/OpenCore-Post-Install/usb/
- **OpenCore Documentation**: https://dortania.github.io/docs/latest/Configuration.html

## Important Notes
- **Every laptop is different** - this guide provides general principles
- **Test thoroughly** after any changes
- **Keep backups** of working configurations
- **Be patient** - USB mapping can take several attempts to get right
