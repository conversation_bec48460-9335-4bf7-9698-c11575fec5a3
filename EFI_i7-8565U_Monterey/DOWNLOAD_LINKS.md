# Download Links and Version Information

## Latest Versions (as of January 2025)

### OpenCore Bootloader
- **Version**: 1.0.5 (July 7, 2024)
- **Download**: https://github.com/acidanthera/OpenCorePkg/releases/tag/1.0.5
- **Files Needed**:
  - `BOOTx64.efi` → `EFI/BOOT/BOOTx64.efi`
  - `OpenCore.efi` → `EFI/OC/OpenCore.efi`
  - `OpenRuntime.efi` → `EFI/OC/Drivers/OpenRuntime.efi`

### Essential Kexts

#### Lilu (Patching Framework)
- **Version**: 1.7.1 (July 8, 2024)
- **Download**: https://github.com/acidanthera/Lilu/releases/tag/1.7.1
- **File**: `Lilu-1.7.1-RELEASE.zip`
- **Required**: Yes (foundation for other kexts)

#### VirtualSMC (System Management Controller)
- **Version**: 1.3.7 (July 8, 2024)
- **Download**: https://github.com/acidanthera/VirtualSMC/releases/tag/1.3.7
- **File**: `VirtualSMC-1.3.7-RELEASE.zip`
- **Includes**:
  - `VirtualSMC.kext` (main SMC emulation)
  - `SMCProcessor.kext` (CPU temperature monitoring)
  - `SMCSuperIO.kext` (fan monitoring)
  - `SMCBatteryManager.kext` (battery monitoring for laptops)

#### WhateverGreen (Graphics Acceleration)
- **Version**: 1.7.0 (July 8, 2024)
- **Download**: https://github.com/acidanthera/WhateverGreen/releases/tag/1.7.0
- **File**: `WhateverGreen-1.7.0-RELEASE.zip`
- **Purpose**: Intel UHD 620 graphics acceleration

#### AppleALC (Audio Support)
- **Version**: 1.9.5 (July 8, 2024)
- **Download**: https://github.com/acidanthera/AppleALC/releases/tag/1.9.5
- **File**: `AppleALC-1.9.5-RELEASE.zip`
- **Purpose**: Audio codec support for laptop audio

#### VoodooPS2Controller (Keyboard/Trackpad)
- **Version**: 2.3.7 (December 4, 2024)
- **Download**: https://github.com/acidanthera/VoodooPS2/releases/tag/2.3.7
- **File**: `VoodooPS2Controller-2.3.7-RELEASE.zip`
- **Purpose**: PS/2 keyboard and trackpad support

#### NVMeFix (SSD Optimization)
- **Version**: 1.1.3 (July 8, 2024)
- **Download**: https://github.com/acidanthera/NVMeFix/releases/tag/1.1.3
- **File**: `NVMeFix-1.1.3-RELEASE.zip`
- **Purpose**: NVMe SSD optimization and power management

#### USBToolBox (USB Mapping)
- **Version**: Latest from GitHub
- **Download**: https://github.com/USBToolBox/kext/releases
- **File**: `USBToolBox-1.1.1-RELEASE.zip` (or latest)
- **Purpose**: USB port mapping framework

### Optional Kexts (Depending on Hardware)

#### VoodooI2C (I2C Trackpad Support)
- **Version**: 2.9.1 (November 7, 2024)
- **Download**: https://github.com/VoodooI2C/VoodooI2C/releases/tag/v2.9.1
- **File**: `VoodooI2C-2.9.1-RELEASE.zip`
- **Use If**: Your laptop has I2C trackpad instead of PS/2

#### BrightnessKeys (Brightness Control)
- **Version**: 1.0.3 (April 4, 2023)
- **Download**: https://github.com/acidanthera/BrightnessKeys/releases/tag/1.0.3
- **File**: `BrightnessKeys-1.0.3-RELEASE.zip`
- **Use If**: Brightness keys don't work with SSDT-PNLF alone

### UEFI Drivers

#### HfsPlus Driver
- **Download**: https://github.com/acidanthera/OcBinaryData/blob/master/Drivers/HfsPlus.efi
- **File**: `HfsPlus.efi` → `EFI/OC/Drivers/HfsPlus.efi`
- **Purpose**: HFS+ filesystem support for macOS recovery

### ACPI Files (SSDTs)

#### Pre-compiled SSDTs
- **Source**: https://github.com/dortania/Getting-Started-With-ACPI/tree/master/extra-files/compiled
- **Required Files**:
  - `SSDT-PLUG-DRTNIA.aml` → `SSDT-PLUG.aml` (CPU power management)
  - `SSDT-EC-USBX-LAPTOP.aml` → `SSDT-EC-USBX.aml` (embedded controller)
  - `SSDT-PNLF.aml` (backlight control)

#### Optional ACPI Files
- **SSDT-XOSI.aml**: For trackpad compatibility (if needed)
- **SSDT-AWAC.aml**: For RTC issues (if needed)

## Tools and Utilities

### GenSMBIOS (SMBIOS Generation)
- **Download**: https://github.com/corpnewt/GenSMBIOS
- **Purpose**: Generate unique SMBIOS values
- **Usage**: Generate MacBookPro15,2 SMBIOS

### USBToolBox Tool (USB Mapping)
- **Download**: https://github.com/USBToolBox/tool/releases
- **Files**:
  - `Windows.exe` (for Windows)
  - `macOS` (for macOS)
- **Purpose**: Create USB port mapping

### ProperTree (Plist Editor)
- **Download**: https://github.com/corpnewt/ProperTree
- **Purpose**: Edit config.plist files
- **Alternative**: Xcode (macOS only)

### Hackintool (System Information)
- **Download**: https://github.com/headkaze/Hackintool/releases
- **Purpose**: System information and patching tool
- **Platform**: macOS only

### IORegistryExplorer (Hardware Analysis)
- **Download**: Apple Developer (requires free account)
- **Purpose**: Analyze hardware configuration
- **Platform**: macOS only

## macOS Monterey Installer

### Download Methods

#### From macOS
1. **App Store**: Direct download (if compatible Mac available)
2. **macrecovery.py**: From OpenCore package
   ```bash
   python macrecovery.py -b Mac-FFE5EF870D7BA81A -m ***************** download
   ```

#### From Windows/Linux
1. **macrecovery.py**: From OpenCore package
2. **gibMacOS**: Alternative download tool
   - Download: https://github.com/corpnewt/gibMacOS

### Installer Verification
- **Size**: ~12GB for full installer
- **Checksum**: Verify with known good checksums
- **Source**: Only use official Apple sources

## Version Compatibility Matrix

### macOS Monterey Support
| Component | Minimum Version | Recommended Version |
|-----------|----------------|-------------------|
| OpenCore | 0.7.0 | 1.0.5 |
| Lilu | 1.5.0 | 1.7.1 |
| WhateverGreen | 1.5.0 | 1.7.0 |
| VirtualSMC | 1.2.0 | 1.3.7 |
| AppleALC | 1.6.0 | 1.9.5 |

### Hardware Compatibility
- **CPU**: Intel 8th Gen (Whiskey Lake) ✅
- **Graphics**: Intel UHD 620 ✅
- **WiFi**: BCM94360NG ✅ (Native)
- **Audio**: Various codecs (layout-id dependent)

## Update Schedule

### Checking for Updates
1. **OpenCore**: Check monthly for updates
2. **Kexts**: Check quarterly for updates
3. **macOS**: Wait for community compatibility confirmation

### Update Process
1. **Backup current EFI** before updating
2. **Test updates** on separate partition if possible
3. **Update one component** at a time
4. **Verify stability** before updating next component

## Mirror Links

### Primary Sources
- **acidanthera**: Main developer of OpenCore and kexts
- **Dortania**: Documentation and guides
- **GitHub**: Primary hosting platform

### Backup Sources
- **InsanelyMac**: Community mirrors
- **tonymacx86**: Alternative downloads (use with caution)

## Verification

### File Integrity
- **Check file sizes** against official releases
- **Verify checksums** when available
- **Use official sources** only

### Security
- **Scan downloads** with antivirus
- **Verify digital signatures** when available
- **Avoid modified versions** from unofficial sources

## Important Notes

### Legal Considerations
- **Only download macOS** if you own a Mac
- **Respect software licenses** and terms of service
- **Educational purposes** only

### Version Compatibility
- **Always check compatibility** before updating
- **Test thoroughly** after any updates
- **Keep working backups** of all configurations

### Support
- **Use latest stable versions** for best support
- **Check release notes** for breaking changes
- **Follow official documentation** for updates

**Last Updated**: January 2025
**Next Review**: April 2025
