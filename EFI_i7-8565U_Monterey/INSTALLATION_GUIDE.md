# Complete Installation Guide for Intel i7-8565U Whiskey Lake Laptop + macOS Monterey

## ⚠️ Important Warnings

**READ BEFORE PROCEEDING:**
- This guide is for educational purposes only
- Hackintosh installations void warranties and may violate EULAs
- Always backup your data before proceeding
- This configuration is a template - customize for your specific laptop
- Results may vary depending on your exact hardware configuration

## Prerequisites

### Hardware Requirements
- Intel Core i7-8565U (Whiskey Lake) processor
- Intel UHD Graphics 620
- 8GB+ RAM (4GB minimum but not recommended)
- 256GB+ storage (SSD recommended)
- BCM94360NG WiFi card (for native support)
- USB flash drive (16GB+)

### Software Requirements
- macOS Monterey installer
- OpenCore 1.0.5
- Required kexts (see download section)
- GenSMBIOS tool
- ProperTree or Xcode for plist editing

## Phase 1: Preparation

### Step 1: Download macOS Monterey
1. **From Mac**: Download from App Store or use `macrecovery.py`
2. **From Windows/Linux**: Use `macrecovery.py` from OpenCore package
3. **Command example**:
   ```bash
   python macrecovery.py -b Mac-FFE5EF870D7BA81A -m 00000000000000000 download
   ```

### Step 2: Create Installation Media
1. **Format USB drive** as FAT32 with GUID partition scheme
2. **Create EFI partition** (200MB minimum)
3. **Copy macOS installer** to main partition
4. **Copy EFI folder** to EFI partition

### Step 3: Download Required Files
Download all files listed in README.md:
- OpenCore 1.0.5
- All required kexts
- ACPI files (SSDT-PLUG, SSDT-EC-USBX, SSDT-PNLF)
- HfsPlus.efi driver

### Step 4: Generate SMBIOS
1. **Download GenSMBIOS**: https://github.com/corpnewt/GenSMBIOS
2. **Run GenSMBIOS** and select option 1 to download MacSerial
3. **Generate SMBIOS** for MacBookPro15,2
4. **Copy values** to config.plist:
   - SystemSerialNumber
   - SystemUUID
   - MLB (Board Serial)
   - ROM (use your network adapter MAC address)

## Phase 2: BIOS Configuration

### Step 1: Update BIOS
- Update to latest BIOS version from manufacturer
- Reset BIOS to defaults before making changes

### Step 2: Configure BIOS Settings
**Follow BIOS_SETUP.md for detailed instructions**

Critical settings:
- **Secure Boot**: Disabled
- **VT-d**: Disabled
- **CFG Lock**: Disabled (if available)
- **SATA Mode**: AHCI
- **XHCI Hand-off**: Enabled

## Phase 3: EFI Configuration

### Step 1: Organize EFI Structure
```
EFI/
├── BOOT/
│   └── BOOTx64.efi
└── OC/
    ├── ACPI/
    │   ├── SSDT-PLUG.aml
    │   ├── SSDT-EC-USBX.aml
    │   └── SSDT-PNLF.aml
    ├── Drivers/
    │   ├── OpenRuntime.efi
    │   └── HfsPlus.efi
    ├── Kexts/
    │   ├── Lilu.kext
    │   ├── VirtualSMC.kext
    │   ├── WhateverGreen.kext
    │   ├── AppleALC.kext
    │   ├── VoodooPS2Controller.kext
    │   ├── NVMeFix.kext
    │   └── USBToolBox.kext
    ├── Tools/
    ├── OpenCore.efi
    └── config.plist
```

### Step 2: Configure config.plist
1. **Use provided config.plist** as base
2. **Update SMBIOS values** with your generated serials
3. **Verify all paths** match your EFI structure
4. **Validate config.plist** using ocvalidate

### Step 3: Audio Configuration
**Audio codec varies by laptop model. Common options:**
- ALC256: Try layout-id 13, 21, 56, 97
- ALC295: Try layout-id 3, 28, 99
- ALC225: Try layout-id 13, 28, 33

Update in config.plist → DeviceProperties → PciRoot(0x0)/Pci(0x1f,0x3) → layout-id

## Phase 4: Installation

### Step 1: Boot from USB
1. **Insert USB installer** into laptop
2. **Boot from USB** (F12, F2, or DEL during startup)
3. **Select USB device** from boot menu
4. **Wait for OpenCore menu** to appear

### Step 2: Install macOS
1. **Select "Install macOS Monterey"** from OpenCore menu
2. **Wait for installer** to load (may take 10-15 minutes)
3. **Use Disk Utility** to format target drive as APFS
4. **Run installer** and follow on-screen instructions
5. **System will reboot** multiple times during installation

### Step 3: Complete Setup
1. **Boot from USB** after each reboot until installation completes
2. **Complete macOS setup** (language, user account, etc.)
3. **Skip Apple ID** during initial setup (add later if desired)

## Phase 5: Post-Installation

### Step 1: Copy EFI to Internal Drive
1. **Mount EFI partition** of internal drive
2. **Copy EFI folder** from USB to internal EFI partition
3. **Test booting** from internal drive
4. **Remove USB installer** once confirmed working

### Step 2: USB Mapping
**Follow USB_MAPPING_GUIDE.md for detailed instructions**
1. **Run USBToolBox tool** to map USB ports
2. **Generate UTBMap.kext**
3. **Enable UTBMap.kext** in config.plist
4. **Disable XhciPortLimit** quirk
5. **Test all USB ports** and sleep/wake functionality

### Step 3: Audio Configuration
1. **Test audio output** with different layout-ids
2. **Update config.plist** with working layout-id
3. **Test microphone** if needed
4. **Verify audio after sleep/wake**

### Step 4: Graphics Optimization
1. **Verify graphics acceleration** in About This Mac
2. **Test external displays** if applicable
3. **Adjust display scaling** if needed
4. **Test brightness controls**

### Step 5: Power Management
1. **Test sleep/wake** functionality
2. **Monitor battery life** and charging
3. **Check CPU frequency scaling** with Intel Power Gadget
4. **Verify thermal management**

## Phase 6: Optimization

### Step 1: Enable Additional Features
- **iCloud/Apple ID**: Sign in after confirming stable operation
- **AirDrop/Handoff**: Should work with BCM94360NG
- **FileVault**: Can be enabled after confirming stability

### Step 2: Performance Tuning
- **Disable unnecessary startup items**
- **Optimize energy settings**
- **Monitor system performance**

### Step 3: Backup Configuration
- **Create Time Machine backup**
- **Backup working EFI folder**
- **Document your configuration**

## Troubleshooting

### Boot Issues
- **Kernel panic**: Check kext compatibility and ACPI patches
- **Black screen**: Try different graphics platform-id
- **Stuck at Apple logo**: Enable verbose mode (-v boot arg)

### Hardware Issues
- **No audio**: Try different layout-ids
- **No WiFi**: Verify BCM94360NG installation
- **Trackpad not working**: May need VoodooI2C instead of VoodooPS2
- **Sleep issues**: Complete USB mapping first

### Performance Issues
- **Slow graphics**: Verify WhateverGreen and graphics properties
- **Poor battery life**: Check power management configuration
- **Thermal throttling**: Monitor temperatures and fan curves

## Maintenance

### Regular Tasks
- **Keep EFI backup** updated
- **Monitor for macOS updates** (test compatibility first)
- **Update kexts** periodically (test thoroughly)
- **Check for BIOS updates** (may require reconfiguration)

### Before Updates
- **Backup current working configuration**
- **Research compatibility** with new macOS versions
- **Test updates** on separate partition if possible
- **Have recovery plan** ready

## Support Resources

### Documentation
- **Dortania OpenCore Guide**: https://dortania.github.io/OpenCore-Install-Guide/
- **OpenCore Documentation**: https://github.com/acidanthera/OpenCorePkg/tree/master/Docs

### Communities
- **r/hackintosh**: Reddit community
- **tonymacx86**: Forums and guides
- **InsanelyMac**: Forums and resources

### Tools
- **OpenCore Configurator**: GUI for config.plist editing
- **Hackintool**: System information and patching
- **IORegistryExplorer**: Hardware analysis

## Final Notes

- **This is a template** - customize for your specific laptop model
- **Test thoroughly** before relying on the system
- **Keep backups** of working configurations
- **Be patient** - hackintosh setup requires time and persistence
- **Respect licenses** and use only legally obtained software

**Good luck with your hackintosh build!**
