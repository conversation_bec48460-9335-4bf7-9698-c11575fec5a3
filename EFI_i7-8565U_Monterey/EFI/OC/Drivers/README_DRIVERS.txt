DRIVERS FOLDER - Required Files
===============================

This folder needs the following files:

1. OpenRuntime.efi
   - Source: OpenCore 1.0.5 release
   - Download: https://github.com/acidanthera/OpenCorePkg/releases/tag/1.0.5
   - Extract: OpenCore-1.0.5-RELEASE.zip → X64/EFI/OC/Drivers/OpenRuntime.efi
   - Copy to: EFI/OC/Drivers/OpenRuntime.efi

2. HfsPlus.efi
   - Source: OcBinaryData repository
   - Download: https://github.com/acidanthera/OcBinaryData/raw/master/Drivers/HfsPlus.efi
   - Copy to: EFI/OC/Drivers/HfsPlus.efi

DRIVER DESCRIPTIONS:
- OpenRuntime.efi: Essential OpenCore runtime driver
- HfsPlus.efi: HFS+ filesystem support for macOS recovery

IMPORTANT: Both files are required for proper OpenCore operation!
