TOOLS FOLDER - Optional Files
=============================

This folder can contain optional OpenCore tools. Common tools include:

1. ResetNVRAM.efi (Optional but recommended)
   - Source: OpenCore 1.0.5 release
   - Download: https://github.com/acidanthera/OpenCorePkg/releases/tag/1.0.5
   - Extract: OpenCore-1.0.5-RELEASE.zip → X64/EFI/OC/Tools/ResetNVRAM.efi
   - Copy to: EFI/OC/Tools/ResetNVRAM.efi

2. CleanNvram.efi (Alternative to ResetNVRAM)
   - Source: Same as above
   - Extract: CleanNvram.efi
   - Copy to: EFI/OC/Tools/CleanNvram.efi

3. VerifyMsrE2.efi (For checking CFG Lock status)
   - Source: Same as above
   - Extract: VerifyMsrE2.efi
   - Copy to: EFI/OC/Tools/VerifyMsrE2.efi

TOOL DESCRIPTIONS:
- ResetNVRAM.efi: Reset NVRAM from OpenCore menu
- CleanNvram.efi: Alternative NVRAM reset tool
- VerifyMsrE2.efi: Check CFG Lock status

NOTE: Tools must be enabled in config.plist to appear in OpenCore menu.
For basic setup, you can leave this folder empty initially.
