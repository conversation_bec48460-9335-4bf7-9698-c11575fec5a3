ACPI FOLDER - Required Files
============================

This folder needs the following compiled ACPI files (.aml files):

1. SSDT-PLUG.aml
   - Source: SSDT-PLUG-DRTNIA.aml (for mobile processors)
   - Download: https://github.com/dortania/Getting-Started-With-ACPI/raw/master/extra-files/compiled/SSDT-PLUG-DRTNIA.aml
   - Rename to: SSDT-PLUG.aml
   - Copy to: EFI/OC/ACPI/SSDT-PLUG.aml

2. SSDT-EC-USBX.aml
   - Source: SSDT-EC-USBX-LAPTOP.aml (for laptops)
   - Download: https://github.com/dortania/Getting-Started-With-ACPI/raw/master/extra-files/compiled/SSDT-EC-USBX-LAPTOP.aml
   - Rename to: SSDT-EC-USBX.aml
   - Copy to: EFI/OC/ACPI/SSDT-EC-USBX.aml

3. SSDT-PNLF.aml
   - Source: Pre-compiled SSDT for backlight control
   - Download: https://github.com/dortania/Getting-Started-With-ACPI/raw/master/extra-files/compiled/SSDT-PNLF.aml
   - Copy to: EFI/OC/ACPI/SSDT-PNLF.aml

OPTIONAL (if needed for trackpad):
4. SSDT-XOSI.aml
   - Source: For trackpad compatibility
   - Download: https://github.com/dortania/Getting-Started-With-ACPI/raw/master/extra-files/compiled/SSDT-XOSI.aml
   - Copy to: EFI/OC/ACPI/SSDT-XOSI.aml
   - Note: Only add if trackpad doesn't work with VoodooPS2

ACPI DESCRIPTIONS:
- SSDT-PLUG.aml: CPU power management for Whiskey Lake mobile
- SSDT-EC-USBX.aml: Embedded controller and USB power for laptops
- SSDT-PNLF.aml: Backlight control for laptop displays
- SSDT-XOSI.aml: Operating system interface patches (optional)

IMPORTANT: These are compiled .aml files, not source .dsl files!
