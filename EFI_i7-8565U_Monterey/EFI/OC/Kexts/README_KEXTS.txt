KEXTS FOLDER - Required Files
=============================

This folder needs the following kext files (extract .kext folders from downloads):

1. Lilu.kext (v1.7.1)
   - Download: https://github.com/acidanthera/Lilu/releases/tag/1.7.1
   - File: Lilu-1.7.1-RELEASE.zip
   - Extract: Lilu.kext folder
   - Copy to: EFI/OC/Kexts/Lilu.kext

2. VirtualSMC.kext (v1.3.7)
   - Download: https://github.com/acidanthera/VirtualSMC/releases/tag/1.3.7
   - File: VirtualSMC-1.3.7-RELEASE.zip
   - Extract: VirtualSMC.kext folder
   - Copy to: EFI/OC/Kexts/VirtualSMC.kext

3. SMCProcessor.kext (from VirtualSMC package)
   - Source: Same as VirtualSMC
   - Extract: SMCProcessor.kext folder
   - Copy to: EFI/OC/Kexts/SMCProcessor.kext

4. SMCSuperIO.kext (from VirtualSMC package)
   - Source: Same as VirtualSMC
   - Extract: SMCSuperIO.kext folder
   - Copy to: EFI/OC/Kexts/SMCSuperIO.kext

5. SMCBatteryManager.kext (from VirtualSMC package)
   - Source: Same as VirtualSMC
   - Extract: SMCBatteryManager.kext folder
   - Copy to: EFI/OC/Kexts/SMCBatteryManager.kext

6. WhateverGreen.kext (v1.7.0)
   - Download: https://github.com/acidanthera/WhateverGreen/releases/tag/1.7.0
   - File: WhateverGreen-1.7.0-RELEASE.zip
   - Extract: WhateverGreen.kext folder
   - Copy to: EFI/OC/Kexts/WhateverGreen.kext

7. AppleALC.kext (v1.9.5)
   - Download: https://github.com/acidanthera/AppleALC/releases/tag/1.9.5
   - File: AppleALC-1.9.5-RELEASE.zip
   - Extract: AppleALC.kext folder
   - Copy to: EFI/OC/Kexts/AppleALC.kext

8. VoodooPS2Controller.kext (v2.3.7)
   - Download: https://github.com/acidanthera/VoodooPS2/releases/tag/2.3.7
   - File: VoodooPS2Controller-2.3.7-RELEASE.zip
   - Extract: VoodooPS2Controller.kext folder
   - Copy to: EFI/OC/Kexts/VoodooPS2Controller.kext

9. NVMeFix.kext (v1.1.3)
   - Download: https://github.com/acidanthera/NVMeFix/releases/tag/1.1.3
   - File: NVMeFix-1.1.3-RELEASE.zip
   - Extract: NVMeFix.kext folder
   - Copy to: EFI/OC/Kexts/NVMeFix.kext

10. USBToolBox.kext (latest)
    - Download: https://github.com/USBToolBox/kext/releases
    - File: USBToolBox-1.1.1-RELEASE.zip (or latest)
    - Extract: USBToolBox.kext folder
    - Copy to: EFI/OC/Kexts/USBToolBox.kext

11. UTBMap.kext (GENERATE AFTER INSTALLATION)
    - This will be created using USBToolBox tool after macOS installation
    - Initially disabled in config.plist
    - Enable after generating with USBToolBox

KEXT DESCRIPTIONS:
- Lilu.kext: Patching framework (required first)
- VirtualSMC.kext: System Management Controller emulation
- SMC*.kext: Hardware monitoring (CPU, fans, battery)
- WhateverGreen.kext: Graphics acceleration for Intel UHD 620
- AppleALC.kext: Audio codec support
- VoodooPS2Controller.kext: Keyboard and trackpad support
- NVMeFix.kext: NVMe SSD optimization
- USBToolBox.kext: USB mapping framework
- UTBMap.kext: Custom USB port mapping (post-install)

IMPORTANT: All kext files must be complete .kext folders, not just the executable files!
