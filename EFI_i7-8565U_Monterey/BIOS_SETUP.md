# BIOS/UEFI Settings for Intel i7-8565U Whiskey Lake Laptop

## Important Notice
⚠️ **BIOS settings vary significantly between laptop manufacturers and models. These are general guidelines that may need adjustment for your specific laptop.**

## Required BIOS Settings

### Security Settings
- **Secure Boot**: Disabled
- **Intel Platform Trust Technology (PTT)**: Disabled (if present)
- **Intel Software Guard Extensions (SGX)**: Disabled (if present)

### Boot Settings
- **Boot Mode**: UEFI (not Legacy/CSM)
- **Fast Boot**: Disabled
- **Boot Device Control**: UEFI Only
- **Network Boot**: Disabled (optional)

### CPU Settings
- **Intel Virtualization Technology (VT-x)**: Enabled
- **Intel VT-d**: Disabled (important for hackintosh)
- **Hyper-Threading**: Enabled
- **Intel SpeedStep**: Enabled
- **Turbo Boost**: Enabled
- **C-States**: Enabled

### Graphics Settings
- **Primary Display**: Auto or Internal Graphics
- **Graphics Configuration**: UMA Only (if discrete GPU present, disable it)
- **DVMT Pre-Allocated**: 64MB or higher (if available)
- **DVMT Total Gfx Mem**: MAX or 256MB+ (if available)

### Storage Settings
- **SATA Mode**: AHCI (not RAID or IDE)
- **NVMe Support**: Enabled

### USB Settings
- **XHCI Hand-off**: Enabled
- **Legacy USB Support**: Enabled (disable after USB mapping)
- **USB Mass Storage Driver Support**: Enabled

### Power Management
- **Intel Speed Shift**: Enabled (if available)
- **CPU Power Management**: Enabled
- **Wake on LAN**: Disabled
- **Wake on USB**: Disabled (initially, can enable later)

### Advanced Settings
- **Above 4G Decoding**: Enabled (if available)
- **Resizable BAR**: Disabled (if available)
- **CFG Lock**: Disabled (if available - very important)

## Manufacturer-Specific Notes

### Dell Laptops
- Look for "Legacy Option ROMs" and disable
- Check "UEFI Network Stack" and disable
- "Wireless Radio Control" should be enabled

### HP Laptops
- "Legacy Support" should be disabled
- "Secure Boot" is usually in Security tab
- Check for "System Configuration" → "Device Configurations"

### Lenovo Laptops
- "CSM Support" should be disabled
- Check "Startup" tab for boot options
- "Intel AMT" should be disabled if present

### ASUS Laptops
- "Launch CSM" should be disabled
- Check "Advanced" → "CPU Configuration"
- "Fast Boot" is usually in Boot tab

## CFG Lock Information
**CFG Lock is critical for proper operation:**

### If CFG Lock Cannot Be Disabled in BIOS:
1. You may need to use `AppleCpuPmCfgLock` and `AppleXcpmCfgLock` quirks
2. Some laptops require BIOS modification to disable CFG Lock
3. Research your specific laptop model for CFG Lock solutions

### Checking CFG Lock Status:
1. Boot into OpenCore
2. Use `ControlMsrE2.efi` tool to check status
3. If locked, enable the CFG Lock quirks in config.plist

## BIOS Update Recommendations
- **Update to latest BIOS** before installation
- **Backup current BIOS** settings before making changes
- **Reset to defaults** first, then apply hackintosh settings
- **Save profiles** if your BIOS supports it

## Common Issues

### Cannot Find Setting
- Setting names vary between manufacturers
- Some settings may be hidden in sub-menus
- Some laptops have limited BIOS options

### Setting Keeps Reverting
- Some laptops reset certain settings on reboot
- May need to set on every boot until macOS is installed
- Consider BIOS modification if persistent

### Missing Critical Settings
- Some budget laptops lack important settings
- May require additional OpenCore quirks
- Research laptop-specific hackintosh guides

## Verification Steps
After changing BIOS settings:
1. **Save and Exit** BIOS
2. **Boot from USB** installer
3. **Check OpenCore logs** for any BIOS-related errors
4. **Adjust settings** if boot fails

## Emergency Recovery
If laptop won't boot after BIOS changes:
1. **Clear CMOS** (remove battery/use reset jumper)
2. **Reset to defaults** in BIOS
3. **Re-apply settings** one by one
4. **Test boot** after each critical change

## Additional Resources
- **Laptop-specific guides**: Search for your exact model
- **BIOS modification tools**: If CFG Lock cannot be disabled
- **OpenCore documentation**: For quirk alternatives to BIOS settings

**Remember**: Always document your original BIOS settings before making changes!
