# EFI Package for Intel i7-8565U Whiskey Lake Laptop + macOS Monterey

## Hardware Configuration
- **CPU**: Intel Core i7-8565U (Whiskey Lake, 4C/8T, 1.8-4.6GHz)
- **GPU**: Intel UHD Graphics 620
- **WiFi Card**: BCM94360NG (Native macOS Support)
- **RAM**: 8GB DDR4 (recommended upgrade from 4GB)
- **Storage**: 256GB M.2 NVMe SSD
- **Display**: 15.6" FHD (1920x1080)
- **Target OS**: macOS Monterey (12.x)

## Software Versions (Latest as of January 2025)
- **OpenCore**: 1.0.5 (July 7, 2024)
- **Lilu**: 1.7.1 (July 8, 2024)
- **WhateverGreen**: 1.7.0 (July 8, 2024)
- **VirtualSMC**: 1.3.7 (July 8, 2024)
- **AppleALC**: 1.9.5 (July 8, 2024)
- **VoodooPS2Controller**: 2.3.7 (December 4, 2024)
- **NVMeFix**: 1.1.3 (July 8, 2024)
- **USBToolBox**: Latest from GitHub

## Download Required Files

### Automated Download (Recommended)
Use the provided scripts to automatically download all required files:

**Linux/macOS**: `./download_files.sh`
**Windows**: `download_files.bat`

After downloading, verify the EFI structure:
**Verification**: `./verify_efi.sh`

### Manual Download

### 1. OpenCore Files
Download OpenCore 1.0.5 from: https://github.com/acidanthera/OpenCorePkg/releases/tag/1.0.5

Extract and copy:
- `BOOTx64.efi` → `EFI/BOOT/BOOTx64.efi`
- `OpenCore.efi` → `EFI/OC/OpenCore.efi`
- `OpenRuntime.efi` → `EFI/OC/Drivers/OpenRuntime.efi`

### 2. HfsPlus Driver
Download from: https://github.com/acidanthera/OcBinaryData/blob/master/Drivers/HfsPlus.efi
Copy to: `EFI/OC/Drivers/HfsPlus.efi`

### 3. Required Kexts
Download all kexts from their respective GitHub releases:

1. **Lilu.kext** (v1.7.1): https://github.com/acidanthera/Lilu/releases/tag/1.7.1
2. **VirtualSMC.kext** (v1.3.7): https://github.com/acidanthera/VirtualSMC/releases/tag/1.3.7
   - Also includes: SMCProcessor.kext, SMCSuperIO.kext, SMCBatteryManager.kext
3. **WhateverGreen.kext** (v1.7.0): https://github.com/acidanthera/WhateverGreen/releases/tag/1.7.0
4. **AppleALC.kext** (v1.9.5): https://github.com/acidanthera/AppleALC/releases/tag/1.9.5
5. **VoodooPS2Controller.kext** (v2.3.7): https://github.com/acidanthera/VoodooPS2/releases/tag/2.3.7
6. **NVMeFix.kext** (v1.1.3): https://github.com/acidanthera/NVMeFix/releases/tag/1.1.3
7. **USBToolBox.kext**: https://github.com/USBToolBox/kext/releases

Copy all kexts to: `EFI/OC/Kexts/`

### 4. ACPI Files (Laptop-Specific)
Download compiled ACPI files from: https://github.com/dortania/Getting-Started-With-ACPI/tree/master/extra-files/compiled

Required files for Whiskey Lake laptops:
- `SSDT-PLUG-DRTNIA.aml` → `EFI/OC/ACPI/SSDT-PLUG.aml`
- `SSDT-EC-USBX-LAPTOP.aml` → `EFI/OC/ACPI/SSDT-EC-USBX.aml`
- `SSDT-PNLF.aml` → `EFI/OC/ACPI/SSDT-PNLF.aml`
- `SSDT-XOSI.aml` → `EFI/OC/ACPI/SSDT-XOSI.aml` (if needed for trackpad)

## Configuration Notes

### SMBIOS
- **Model**: MacBookPro15,2 (optimal for Whiskey Lake + Monterey)
- **Generate new serials**: Use GenSMBIOS to generate unique serials
- **Current serials are INVALID** - replace before use

### Graphics (Intel UHD 620)
- **Device-ID**: 0x9B3E0000 (required for Whiskey Lake)
- **Platform-ID**: 0x3EA50009 (with display outputs)
- **Alternative**: 0x3EA50000 (if display issues occur)
- **VRAM**: 2048MB allocated

### Audio
- **Codec**: Varies by laptop model - common options:
  - ALC256 (layout-id: 13, 21, 56, 97)
  - ALC295 (layout-id: 3, 28, 99)
  - ALC225 (layout-id: 13, 28, 33)
- **Test different layout-ids** until audio works

### USB Mapping
- **Initial**: XhciPortLimit=true (for installation only)
- **Post-Install**: Generate UTBMap.kext using USBToolBox
- **Disable**: XhciPortLimit=false after USB mapping

### BCM94360NG WiFi/Bluetooth
- **No additional kexts required** - native support
- **Full functionality**: WiFi, Bluetooth, AirDrop, Handoff, Continuity

## Installation Steps

### Quick Setup
1. **Run download script**: `./download_files.sh` (Linux/macOS) or `download_files.bat` (Windows)
2. **Verify EFI**: `./verify_efi.sh` to ensure all files are present
3. **Generate SMBIOS**: Use GenSMBIOS for MacBookPro15,2
4. **Update config.plist**: Replace placeholder serials with your generated values
5. **Create USB installer**: Copy EFI folder to USB installer
6. **Configure BIOS**: Follow BIOS_SETUP.md
7. **Install macOS**: Follow INSTALLATION_GUIDE.md
8. **USB mapping**: Use USB_MAPPING_GUIDE.md after installation

### Detailed Guides
- **Complete Installation**: INSTALLATION_GUIDE.md
- **USB Mapping**: USB_MAPPING_GUIDE.md
- **BIOS Configuration**: BIOS_SETUP.md
- **Troubleshooting**: TROUBLESHOOTING.md
- **Post-Install**: POST_INSTALL_CHECKLIST.md

## What Should Work
✅ **Native WiFi/Bluetooth** (BCM94360NG)
✅ **Graphics Acceleration** (Intel UHD 620)
✅ **Audio** (with correct layout-id)
✅ **Keyboard/Trackpad** (VoodooPS2Controller or VoodooI2C)
✅ **Sleep/Wake** (after USB mapping)
✅ **Power Management** (with SSDT-PLUG-DRTNIA)
✅ **NVMe SSD** (with NVMeFix optimization)
✅ **Backlight Control** (with SSDT-PNLF)

## Support
This configuration is based on Dortania's OpenCore Install Guide and uses the latest stable versions of all components as of January 2025.

**Important**: This is a template configuration. You MUST customize it for your specific laptop model and hardware configuration.
