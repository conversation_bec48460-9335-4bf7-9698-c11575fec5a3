# Complete EFI Download and Setup Guide

## 📁 EFI Structure Overview
```
EFI/
├── BOOT/
│   └── BOOTx64.efi                    ← From OpenCore
└── OC/
    ├── ACPI/
    │   ├── SSDT-PLUG.aml              ← From Dortania ACPI
    │   ├── SSDT-EC-USBX.aml           ← From Dortania ACPI
    │   └── SSDT-PNLF.aml              ← From Dortania ACPI
    ├── Drivers/
    │   ├── OpenRuntime.efi            ← From OpenCore
    │   └── HfsPlus.efi                ← From OcBinaryData
    ├── Kexts/
    │   ├── Lilu.kext                  ← From acidanthera
    │   ├── VirtualSMC.kext            ← From acidanthera
    │   ├── SMCProcessor.kext          ← From VirtualSMC package
    │   ├── SMCSuperIO.kext            ← From VirtualSMC package
    │   ├── SMCBatteryManager.kext     ← From VirtualSMC package
    │   ├── WhateverGreen.kext         ← From acidanthera
    │   ├── AppleALC.kext              ← From acidanthera
    │   ├── VoodooPS2Controller.kext   ← From acidanthera
    │   ├── NVMeFix.kext               ← From acidanthera
    │   ├── USBToolBox.kext            ← From USBToolBox
    │   └── UTBMap.kext                ← Generate after install
    ├── Tools/
    │   └── ResetNVRAM.efi             ← From OpenCore (optional)
    ├── OpenCore.efi                   ← From OpenCore
    └── config.plist                   ← Already provided
```

## 🔽 Step-by-Step Download Instructions

### Step 1: Download OpenCore 1.0.5
1. **Go to**: https://github.com/acidanthera/OpenCorePkg/releases/tag/1.0.5
2. **Download**: `OpenCore-1.0.5-RELEASE.zip`
3. **Extract** the zip file
4. **Copy these files**:
   - `X64/EFI/BOOT/BOOTx64.efi` → `EFI/BOOT/BOOTx64.efi`
   - `X64/EFI/OC/OpenCore.efi` → `EFI/OC/OpenCore.efi`
   - `X64/EFI/OC/Drivers/OpenRuntime.efi` → `EFI/OC/Drivers/OpenRuntime.efi`
   - `X64/EFI/OC/Tools/ResetNVRAM.efi` → `EFI/OC/Tools/ResetNVRAM.efi` (optional)

### Step 2: Download HfsPlus Driver
1. **Direct download**: https://github.com/acidanthera/OcBinaryData/raw/master/Drivers/HfsPlus.efi
2. **Save as**: `EFI/OC/Drivers/HfsPlus.efi`

### Step 3: Download Lilu (v1.7.1)
1. **Go to**: https://github.com/acidanthera/Lilu/releases/tag/1.7.1
2. **Download**: `Lilu-1.7.1-RELEASE.zip`
3. **Extract** and copy `Lilu.kext` folder to `EFI/OC/Kexts/Lilu.kext`

### Step 4: Download VirtualSMC (v1.3.7)
1. **Go to**: https://github.com/acidanthera/VirtualSMC/releases/tag/1.3.7
2. **Download**: `VirtualSMC-1.3.7-RELEASE.zip`
3. **Extract** and copy these kext folders:
   - `VirtualSMC.kext` → `EFI/OC/Kexts/VirtualSMC.kext`
   - `SMCProcessor.kext` → `EFI/OC/Kexts/SMCProcessor.kext`
   - `SMCSuperIO.kext` → `EFI/OC/Kexts/SMCSuperIO.kext`
   - `SMCBatteryManager.kext` → `EFI/OC/Kexts/SMCBatteryManager.kext`

### Step 5: Download WhateverGreen (v1.7.0)
1. **Go to**: https://github.com/acidanthera/WhateverGreen/releases/tag/1.7.0
2. **Download**: `WhateverGreen-1.7.0-RELEASE.zip`
3. **Extract** and copy `WhateverGreen.kext` folder to `EFI/OC/Kexts/WhateverGreen.kext`

### Step 6: Download AppleALC (v1.9.5)
1. **Go to**: https://github.com/acidanthera/AppleALC/releases/tag/1.9.5
2. **Download**: `AppleALC-1.9.5-RELEASE.zip`
3. **Extract** and copy `AppleALC.kext` folder to `EFI/OC/Kexts/AppleALC.kext`

### Step 7: Download VoodooPS2Controller (v2.3.7)
1. **Go to**: https://github.com/acidanthera/VoodooPS2/releases/tag/2.3.7
2. **Download**: `VoodooPS2Controller-2.3.7-RELEASE.zip`
3. **Extract** and copy `VoodooPS2Controller.kext` folder to `EFI/OC/Kexts/VoodooPS2Controller.kext`

### Step 8: Download NVMeFix (v1.1.3)
1. **Go to**: https://github.com/acidanthera/NVMeFix/releases/tag/1.1.3
2. **Download**: `NVMeFix-1.1.3-RELEASE.zip`
3. **Extract** and copy `NVMeFix.kext` folder to `EFI/OC/Kexts/NVMeFix.kext`

### Step 9: Download USBToolBox
1. **Go to**: https://github.com/USBToolBox/kext/releases
2. **Download**: Latest release (e.g., `USBToolBox-1.1.1-RELEASE.zip`)
3. **Extract** and copy `USBToolBox.kext` folder to `EFI/OC/Kexts/USBToolBox.kext`

### Step 10: Download ACPI Files
1. **SSDT-PLUG**: https://github.com/dortania/Getting-Started-With-ACPI/raw/master/extra-files/compiled/SSDT-PLUG-DRTNIA.aml
   - Save as: `EFI/OC/ACPI/SSDT-PLUG.aml`

2. **SSDT-EC-USBX**: https://github.com/dortania/Getting-Started-With-ACPI/raw/master/extra-files/compiled/SSDT-EC-USBX-LAPTOP.aml
   - Save as: `EFI/OC/ACPI/SSDT-EC-USBX.aml`

3. **SSDT-PNLF**: https://github.com/dortania/Getting-Started-With-ACPI/raw/master/extra-files/compiled/SSDT-PNLF.aml
   - Save as: `EFI/OC/ACPI/SSDT-PNLF.aml`

## ✅ Verification Checklist

After downloading all files, verify your EFI structure:

### BOOT Folder
- [ ] `EFI/BOOT/BOOTx64.efi` (from OpenCore)

### OC Folder
- [ ] `EFI/OC/OpenCore.efi` (from OpenCore)
- [ ] `EFI/OC/config.plist` (already provided)

### Drivers Folder
- [ ] `EFI/OC/Drivers/OpenRuntime.efi` (from OpenCore)
- [ ] `EFI/OC/Drivers/HfsPlus.efi` (from OcBinaryData)

### ACPI Folder
- [ ] `EFI/OC/ACPI/SSDT-PLUG.aml`
- [ ] `EFI/OC/ACPI/SSDT-EC-USBX.aml`
- [ ] `EFI/OC/ACPI/SSDT-PNLF.aml`

### Kexts Folder
- [ ] `EFI/OC/Kexts/Lilu.kext/` (complete folder)
- [ ] `EFI/OC/Kexts/VirtualSMC.kext/` (complete folder)
- [ ] `EFI/OC/Kexts/SMCProcessor.kext/` (complete folder)
- [ ] `EFI/OC/Kexts/SMCSuperIO.kext/` (complete folder)
- [ ] `EFI/OC/Kexts/SMCBatteryManager.kext/` (complete folder)
- [ ] `EFI/OC/Kexts/WhateverGreen.kext/` (complete folder)
- [ ] `EFI/OC/Kexts/AppleALC.kext/` (complete folder)
- [ ] `EFI/OC/Kexts/VoodooPS2Controller.kext/` (complete folder)
- [ ] `EFI/OC/Kexts/NVMeFix.kext/` (complete folder)
- [ ] `EFI/OC/Kexts/USBToolBox.kext/` (complete folder)

### Tools Folder (Optional)
- [ ] `EFI/OC/Tools/ResetNVRAM.efi` (from OpenCore)

## 🔧 Additional Setup Steps

### Step 11: Generate SMBIOS
1. **Download GenSMBIOS**: https://github.com/corpnewt/GenSMBIOS
2. **Run GenSMBIOS** and select option 1 to download MacSerial
3. **Generate SMBIOS** for `MacBookPro15,2`
4. **Update config.plist** with generated values:
   - SystemSerialNumber
   - SystemUUID
   - MLB (Board Serial)
   - ROM (use your network adapter MAC address)

### Step 12: Download USBToolBox Tool (for post-install)
1. **Go to**: https://github.com/USBToolBox/tool/releases
2. **Download**: `Windows.exe` (for Windows) or `macOS` (for macOS)
3. **Save for later use** during USB mapping phase

## 📝 Important Notes

### File Types
- **Kexts**: Must be complete `.kext` folders, not just executable files
- **ACPI**: Must be compiled `.aml` files, not source `.dsl` files
- **Drivers**: Must be `.efi` files

### Version Verification
- Check that downloaded versions match the specified versions
- Verify file sizes are reasonable (not corrupted downloads)
- Ensure all files are from official sources only

### Security
- Only download from official GitHub repositories
- Verify checksums when available
- Scan downloads with antivirus software

## 🚀 Next Steps

After completing all downloads:
1. **Copy the complete EFI folder** to your USB installer
2. **Follow INSTALLATION_GUIDE.md** for the installation process
3. **Generate USB mapping** after successful installation
4. **Test all functionality** using POST_INSTALL_CHECKLIST.md

## 🆘 Troubleshooting Downloads

### If downloads fail:
- Try using a VPN or different network
- Use alternative download tools (wget, curl)
- Check GitHub status for service issues

### If files are corrupted:
- Re-download from official sources
- Verify file integrity with checksums
- Try downloading on a different device

**Total download size**: Approximately 50-100MB
**Estimated time**: 30-60 minutes depending on connection speed
