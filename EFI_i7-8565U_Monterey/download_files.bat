@echo off
REM EFI Download Script for Intel i7-8565U Whiskey Lake Hackintosh (Windows)
REM This script downloads all required files for the EFI configuration

echo 🍎 EFI Download Script for Intel i7-8565U Whiskey Lake Hackintosh
echo ==================================================================
echo.

REM Check if curl is available
curl --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Error: curl is not available. Please install curl or use Windows 10/11.
    pause
    exit /b 1
)

REM Create directory structure
echo 📁 Creating EFI directory structure...
mkdir EFI\BOOT 2>nul
mkdir EFI\OC\ACPI 2>nul
mkdir EFI\OC\Drivers 2>nul
mkdir EFI\OC\Kexts 2>nul
mkdir EFI\OC\Tools 2>nul
mkdir downloads\temp 2>nul

echo ✅ Directory structure created
echo.

REM Download OpenCore 1.0.5
echo ⬇️  Downloading OpenCore 1.0.5...
curl -L -o "downloads\OpenCore-1.0.5-RELEASE.zip" "https://github.com/acidanthera/OpenCorePkg/releases/download/1.0.5/OpenCore-1.0.5-RELEASE.zip"

echo 📦 Extracting OpenCore...
powershell -command "Expand-Archive -Path 'downloads\OpenCore-1.0.5-RELEASE.zip' -DestinationPath 'downloads\temp\opencore' -Force"

REM Copy OpenCore files
echo 📋 Copying OpenCore files...
copy "downloads\temp\opencore\X64\EFI\BOOT\BOOTx64.efi" "EFI\BOOT\" >nul
copy "downloads\temp\opencore\X64\EFI\OC\OpenCore.efi" "EFI\OC\" >nul
copy "downloads\temp\opencore\X64\EFI\OC\Drivers\OpenRuntime.efi" "EFI\OC\Drivers\" >nul
copy "downloads\temp\opencore\X64\EFI\OC\Tools\ResetNVRAM.efi" "EFI\OC\Tools\" >nul

REM Download HfsPlus driver
echo ⬇️  Downloading HfsPlus.efi...
curl -L -o "EFI\OC\Drivers\HfsPlus.efi" "https://github.com/acidanthera/OcBinaryData/raw/master/Drivers/HfsPlus.efi"

REM Download Lilu 1.7.1
echo ⬇️  Downloading Lilu 1.7.1...
curl -L -o "downloads\Lilu-1.7.1-RELEASE.zip" "https://github.com/acidanthera/Lilu/releases/download/1.7.1/Lilu-1.7.1-RELEASE.zip"
powershell -command "Expand-Archive -Path 'downloads\Lilu-1.7.1-RELEASE.zip' -DestinationPath 'downloads\temp\lilu' -Force"
xcopy "downloads\temp\lilu\Lilu.kext" "EFI\OC\Kexts\Lilu.kext\" /E /I /Q >nul

REM Download VirtualSMC 1.3.7
echo ⬇️  Downloading VirtualSMC 1.3.7...
curl -L -o "downloads\VirtualSMC-1.3.7-RELEASE.zip" "https://github.com/acidanthera/VirtualSMC/releases/download/1.3.7/VirtualSMC-1.3.7-RELEASE.zip"
powershell -command "Expand-Archive -Path 'downloads\VirtualSMC-1.3.7-RELEASE.zip' -DestinationPath 'downloads\temp\virtualsmc' -Force"
xcopy "downloads\temp\virtualsmc\VirtualSMC.kext" "EFI\OC\Kexts\VirtualSMC.kext\" /E /I /Q >nul
xcopy "downloads\temp\virtualsmc\SMCProcessor.kext" "EFI\OC\Kexts\SMCProcessor.kext\" /E /I /Q >nul
xcopy "downloads\temp\virtualsmc\SMCSuperIO.kext" "EFI\OC\Kexts\SMCSuperIO.kext\" /E /I /Q >nul
xcopy "downloads\temp\virtualsmc\SMCBatteryManager.kext" "EFI\OC\Kexts\SMCBatteryManager.kext\" /E /I /Q >nul

REM Download WhateverGreen 1.7.0
echo ⬇️  Downloading WhateverGreen 1.7.0...
curl -L -o "downloads\WhateverGreen-1.7.0-RELEASE.zip" "https://github.com/acidanthera/WhateverGreen/releases/download/1.7.0/WhateverGreen-1.7.0-RELEASE.zip"
powershell -command "Expand-Archive -Path 'downloads\WhateverGreen-1.7.0-RELEASE.zip' -DestinationPath 'downloads\temp\whatevergreen' -Force"
xcopy "downloads\temp\whatevergreen\WhateverGreen.kext" "EFI\OC\Kexts\WhateverGreen.kext\" /E /I /Q >nul

REM Download AppleALC 1.9.5
echo ⬇️  Downloading AppleALC 1.9.5...
curl -L -o "downloads\AppleALC-1.9.5-RELEASE.zip" "https://github.com/acidanthera/AppleALC/releases/download/1.9.5/AppleALC-1.9.5-RELEASE.zip"
powershell -command "Expand-Archive -Path 'downloads\AppleALC-1.9.5-RELEASE.zip' -DestinationPath 'downloads\temp\applealc' -Force"
xcopy "downloads\temp\applealc\AppleALC.kext" "EFI\OC\Kexts\AppleALC.kext\" /E /I /Q >nul

REM Download VoodooPS2Controller 2.3.7
echo ⬇️  Downloading VoodooPS2Controller 2.3.7...
curl -L -o "downloads\VoodooPS2Controller-2.3.7-RELEASE.zip" "https://github.com/acidanthera/VoodooPS2/releases/download/2.3.7/VoodooPS2Controller-2.3.7-RELEASE.zip"
powershell -command "Expand-Archive -Path 'downloads\VoodooPS2Controller-2.3.7-RELEASE.zip' -DestinationPath 'downloads\temp\voodoops2' -Force"
xcopy "downloads\temp\voodoops2\VoodooPS2Controller.kext" "EFI\OC\Kexts\VoodooPS2Controller.kext\" /E /I /Q >nul

REM Download NVMeFix 1.1.3
echo ⬇️  Downloading NVMeFix 1.1.3...
curl -L -o "downloads\NVMeFix-1.1.3-RELEASE.zip" "https://github.com/acidanthera/NVMeFix/releases/download/1.1.3/NVMeFix-1.1.3-RELEASE.zip"
powershell -command "Expand-Archive -Path 'downloads\NVMeFix-1.1.3-RELEASE.zip' -DestinationPath 'downloads\temp\nvmefix' -Force"
xcopy "downloads\temp\nvmefix\NVMeFix.kext" "EFI\OC\Kexts\NVMeFix.kext\" /E /I /Q >nul

REM Download USBToolBox
echo ⬇️  Downloading USBToolBox...
curl -L -o "downloads\USBToolBox-1.1.1-RELEASE.zip" "https://github.com/USBToolBox/kext/releases/download/1.1.1/USBToolBox-1.1.1-RELEASE.zip"
powershell -command "Expand-Archive -Path 'downloads\USBToolBox-1.1.1-RELEASE.zip' -DestinationPath 'downloads\temp\usbtoolbox' -Force"
xcopy "downloads\temp\usbtoolbox\USBToolBox.kext" "EFI\OC\Kexts\USBToolBox.kext\" /E /I /Q >nul

REM Download ACPI files
echo ⬇️  Downloading ACPI files...
curl -L -o "EFI\OC\ACPI\SSDT-PLUG.aml" "https://github.com/dortania/Getting-Started-With-ACPI/raw/master/extra-files/compiled/SSDT-PLUG-DRTNIA.aml"
curl -L -o "EFI\OC\ACPI\SSDT-EC-USBX.aml" "https://github.com/dortania/Getting-Started-With-ACPI/raw/master/extra-files/compiled/SSDT-EC-USBX-LAPTOP.aml"
curl -L -o "EFI\OC\ACPI\SSDT-PNLF.aml" "https://github.com/dortania/Getting-Started-With-ACPI/raw/master/extra-files/compiled/SSDT-PNLF.aml"

REM Copy config.plist if it exists
if exist "config.plist" (
    echo 📋 Copying config.plist...
    copy "config.plist" "EFI\OC\" >nul
)

REM Cleanup
echo 🧹 Cleaning up temporary files...
rmdir /s /q downloads >nul 2>&1

echo.
echo ✅ Download complete! EFI folder is ready.
echo.
echo 📋 Next steps:
echo 1. Generate SMBIOS with GenSMBIOS (MacBookPro15,2)
echo 2. Update config.plist with your generated serials
echo 3. Copy EFI folder to your USB installer
echo 4. Follow INSTALLATION_GUIDE.md for installation
echo.
echo ⚠️  IMPORTANT: You must generate unique SMBIOS values before use!
echo.
pause
