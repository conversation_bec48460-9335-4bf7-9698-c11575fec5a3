#!/bin/bash

# EFI Download Script for Intel i7-8565U Whiskey Lake Hackintosh
# This script downloads all required files for the EFI configuration

set -e  # Exit on any error

echo "🍎 EFI Download Script for Intel i7-8565U Whiskey Lake Hackintosh"
echo "=================================================================="
echo ""

# Create directory structure
echo "📁 Creating EFI directory structure..."
mkdir -p EFI/BOOT
mkdir -p EFI/OC/ACPI
mkdir -p EFI/OC/Drivers
mkdir -p EFI/OC/Kexts
mkdir -p EFI/OC/Tools
mkdir -p downloads/temp

echo "✅ Directory structure created"
echo ""

# Function to download and extract
download_and_extract() {
    local url=$1
    local filename=$2
    local extract_path=$3
    
    echo "⬇️  Downloading $filename..."
    curl -L -o "downloads/$filename" "$url"
    
    if [[ $filename == *.zip ]]; then
        echo "📦 Extracting $filename..."
        unzip -q "downloads/$filename" -d "downloads/temp/$extract_path"
    fi
}

# Download OpenCore 1.0.5
echo "🔽 Downloading OpenCore 1.0.5..."
download_and_extract "https://github.com/acidanthera/OpenCorePkg/releases/download/1.0.5/OpenCore-1.0.5-RELEASE.zip" "OpenCore-1.0.5-RELEASE.zip" "opencore"

# Copy OpenCore files
echo "📋 Copying OpenCore files..."
cp "downloads/temp/opencore/X64/EFI/BOOT/BOOTx64.efi" "EFI/BOOT/"
cp "downloads/temp/opencore/X64/EFI/OC/OpenCore.efi" "EFI/OC/"
cp "downloads/temp/opencore/X64/EFI/OC/Drivers/OpenRuntime.efi" "EFI/OC/Drivers/"
cp "downloads/temp/opencore/X64/EFI/OC/Tools/ResetNVRAM.efi" "EFI/OC/Tools/"

# Download HfsPlus driver
echo "⬇️  Downloading HfsPlus.efi..."
curl -L -o "EFI/OC/Drivers/HfsPlus.efi" "https://github.com/acidanthera/OcBinaryData/raw/master/Drivers/HfsPlus.efi"

# Download Lilu 1.7.1
echo "⬇️  Downloading Lilu 1.7.1..."
download_and_extract "https://github.com/acidanthera/Lilu/releases/download/1.7.1/Lilu-1.7.1-RELEASE.zip" "Lilu-1.7.1-RELEASE.zip" "lilu"
cp -r "downloads/temp/lilu/Lilu.kext" "EFI/OC/Kexts/"

# Download VirtualSMC 1.3.7
echo "⬇️  Downloading VirtualSMC 1.3.7..."
download_and_extract "https://github.com/acidanthera/VirtualSMC/releases/download/1.3.7/VirtualSMC-1.3.7-RELEASE.zip" "VirtualSMC-1.3.7-RELEASE.zip" "virtualsmc"
cp -r "downloads/temp/virtualsmc/VirtualSMC.kext" "EFI/OC/Kexts/"
cp -r "downloads/temp/virtualsmc/SMCProcessor.kext" "EFI/OC/Kexts/"
cp -r "downloads/temp/virtualsmc/SMCSuperIO.kext" "EFI/OC/Kexts/"
cp -r "downloads/temp/virtualsmc/SMCBatteryManager.kext" "EFI/OC/Kexts/"

# Download WhateverGreen 1.7.0
echo "⬇️  Downloading WhateverGreen 1.7.0..."
download_and_extract "https://github.com/acidanthera/WhateverGreen/releases/download/1.7.0/WhateverGreen-1.7.0-RELEASE.zip" "WhateverGreen-1.7.0-RELEASE.zip" "whatevergreen"
cp -r "downloads/temp/whatevergreen/WhateverGreen.kext" "EFI/OC/Kexts/"

# Download AppleALC 1.9.5
echo "⬇️  Downloading AppleALC 1.9.5..."
download_and_extract "https://github.com/acidanthera/AppleALC/releases/download/1.9.5/AppleALC-1.9.5-RELEASE.zip" "AppleALC-1.9.5-RELEASE.zip" "applealc"
cp -r "downloads/temp/applealc/AppleALC.kext" "EFI/OC/Kexts/"

# Download VoodooPS2Controller 2.3.7
echo "⬇️  Downloading VoodooPS2Controller 2.3.7..."
download_and_extract "https://github.com/acidanthera/VoodooPS2/releases/download/2.3.7/VoodooPS2Controller-2.3.7-RELEASE.zip" "VoodooPS2Controller-2.3.7-RELEASE.zip" "voodoops2"
cp -r "downloads/temp/voodoops2/VoodooPS2Controller.kext" "EFI/OC/Kexts/"

# Download NVMeFix 1.1.3
echo "⬇️  Downloading NVMeFix 1.1.3..."
download_and_extract "https://github.com/acidanthera/NVMeFix/releases/download/1.1.3/NVMeFix-1.1.3-RELEASE.zip" "NVMeFix-1.1.3-RELEASE.zip" "nvmefix"
cp -r "downloads/temp/nvmefix/NVMeFix.kext" "EFI/OC/Kexts/"

# Download USBToolBox
echo "⬇️  Downloading USBToolBox..."
download_and_extract "https://github.com/USBToolBox/kext/releases/download/1.1.1/USBToolBox-1.1.1-RELEASE.zip" "USBToolBox-1.1.1-RELEASE.zip" "usbtoolbox"
cp -r "downloads/temp/usbtoolbox/USBToolBox.kext" "EFI/OC/Kexts/"

# Download ACPI files
echo "⬇️  Downloading ACPI files..."
curl -L -o "EFI/OC/ACPI/SSDT-PLUG.aml" "https://github.com/dortania/Getting-Started-With-ACPI/raw/master/extra-files/compiled/SSDT-PLUG-DRTNIA.aml"
curl -L -o "EFI/OC/ACPI/SSDT-EC-USBX.aml" "https://github.com/dortania/Getting-Started-With-ACPI/raw/master/extra-files/compiled/SSDT-EC-USBX-LAPTOP.aml"
curl -L -o "EFI/OC/ACPI/SSDT-PNLF.aml" "https://github.com/dortania/Getting-Started-With-ACPI/raw/master/extra-files/compiled/SSDT-PNLF.aml"

# Copy config.plist if it exists
if [ -f "config.plist" ]; then
    echo "📋 Copying config.plist..."
    cp "config.plist" "EFI/OC/"
fi

# Cleanup
echo "🧹 Cleaning up temporary files..."
rm -rf downloads/

echo ""
echo "✅ Download complete! EFI folder is ready."
echo ""
echo "📋 Next steps:"
echo "1. Generate SMBIOS with GenSMBIOS (MacBookPro15,2)"
echo "2. Update config.plist with your generated serials"
echo "3. Copy EFI folder to your USB installer"
echo "4. Follow INSTALLATION_GUIDE.md for installation"
echo ""
echo "⚠️  IMPORTANT: You must generate unique SMBIOS values before use!"
echo ""
