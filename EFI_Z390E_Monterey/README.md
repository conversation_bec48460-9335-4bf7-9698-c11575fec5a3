# EFI Package for ASUS ROG Strix Z390-E + i7-9700K + macOS Monterey

## Hardware Configuration
- **Motherboard**: ASUS ROG Strix Z390-E Gaming
- **CPU**: Intel Core i7-9700K (Coffee Lake Refresh)
- **WiFi Card**: FENVi T919 (BCM94360CD) - Native Support
- **Storage**: PNY CS2230 500GB M.2 NVMe SSD
- **RAM**: 16GB (2x8GB) DDR4 2666 MHz
- **Target OS**: macOS Monterey (12.x)

## Software Versions (Latest as of January 2025)
- **OpenCore**: 1.0.5 (July 7, 2024)
- **VirtualSMC**: 1.3.7 (July 7, 2024)
- **Lilu**: 1.7.1 (July 7, 2024)
- **WhateverGreen**: 1.7.0 (July 7, 2024)
- **AppleALC**: 1.9.5 (July 7, 2024)
- **IntelMausi**: 1.0.8 (December 3, 2024)
- **NVMeFix**: 1.1.3 (July 7, 2024)
- **USBToolBox**: 0.2 (June 10, 2024)

## Download Required Files

### 1. OpenCore Files
Download OpenCore 1.0.5 from: https://github.com/acidanthera/OpenCorePkg/releases/tag/1.0.5

Extract and copy:
- `BOOTx64.efi` → `EFI/BOOT/BOOTx64.efi`
- `OpenCore.efi` → `EFI/OC/OpenCore.efi`
- `OpenRuntime.efi` → `EFI/OC/Drivers/OpenRuntime.efi`

### 2. HfsPlus Driver
Download from: https://github.com/acidanthera/OcBinaryData/blob/master/Drivers/HfsPlus.efi
Copy to: `EFI/OC/Drivers/HfsPlus.efi`

### 3. Required Kexts
Download all kexts from their respective GitHub releases:

1. **Lilu.kext** (v1.7.1): https://github.com/acidanthera/Lilu/releases/tag/1.7.1
2. **VirtualSMC.kext** (v1.3.7): https://github.com/acidanthera/VirtualSMC/releases/tag/1.3.7
   - Also includes: SMCProcessor.kext, SMCSuperIO.kext
3. **WhateverGreen.kext** (v1.7.0): https://github.com/acidanthera/WhateverGreen/releases/tag/1.7.0
4. **AppleALC.kext** (v1.9.5): https://github.com/acidanthera/AppleALC/releases/tag/1.9.5
5. **IntelMausi.kext** (v1.0.8): https://github.com/acidanthera/IntelMausi/releases/tag/1.0.8
6. **NVMeFix.kext** (v1.1.3): https://github.com/acidanthera/NVMeFix/releases/tag/1.1.3
7. **USBToolBox.kext** (v0.2): https://github.com/USBToolBox/kext/releases

Copy all kexts to: `EFI/OC/Kexts/`

### 4. ACPI Files
Download compiled ACPI files from: https://github.com/dortania/Getting-Started-With-ACPI/tree/master/extra-files/compiled

Required files:
- `SSDT-PLUG.aml` → `EFI/OC/ACPI/SSDT-PLUG.aml`
- `SSDT-EC-USBX-DESKTOP.aml` → `EFI/OC/ACPI/SSDT-EC-USBX.aml`
- `SSDT-AWAC.aml` → `EFI/OC/ACPI/SSDT-AWAC.aml`
- `SSDT-PMC.aml` → `EFI/OC/ACPI/SSDT-PMC.aml`

## Configuration Notes

### SMBIOS
- **Model**: iMac19,1 (optimal for Coffee Lake + Monterey)
- **Generate new serials**: Use GenSMBIOS to generate unique serials
- **Current serials are INVALID** - replace before use

### Audio
- **Codec**: ALC1220
- **Layout ID**: 1 (default, test 7, 11, 15 if issues)
- **Change in config.plist**: DeviceProperties → PciRoot(0x0)/Pci(0x1f,0x3) → layout-id

### Graphics
- **iGPU**: Intel UHD 630
- **Platform ID**: 0x3E9B0007 (with display outputs)
- **Alternative**: 0x3E980003 (headless mode if using discrete GPU)

### USB Mapping
- **Initial**: XhciPortLimit=true (for installation)
- **Post-Install**: Generate UTBMap.kext using USBToolBox
- **Disable**: XhciPortLimit=false after USB mapping

## Installation Steps
1. Download and organize all files as described above
2. Generate unique SMBIOS with GenSMBIOS
3. Copy EFI folder to USB installer
4. Configure BIOS settings (see BIOS_SETUP.md)
5. Install macOS Monterey
6. Copy EFI to internal drive
7. Generate USB mapping with USBToolBox
8. Disable XhciPortLimit and enable UTBMap.kext

## What Works
✅ **Native WiFi/Bluetooth** (FENVi T919 BCM94360CD)
✅ **Audio** (ALC1220 with AppleALC)
✅ **Ethernet** (Intel I219-V with IntelMausi)
✅ **Graphics Acceleration** (Intel UHD 630)
✅ **Sleep/Wake** (after USB mapping)
✅ **Power Management** (with SSDT-PLUG)
✅ **NVMe SSD** (with NVMeFix optimization)

## Support
This configuration is based on Dortania's OpenCore Install Guide and uses the latest stable versions of all components as of January 2025.
