# Troubleshooting Guide - Z390-<PERSON> Hackintosh

## 🚨 Emergency Recovery

### System Won't Boot
**Symptoms**: Black screen, kernel panic, or boot loop

**Immediate Actions**:
1. **Boot from USB installer** with working EFI
2. **Mount internal EFI** partition:
   ```bash
   sudo diskutil mount disk0s1
   ```
3. **Restore backup config.plist**:
   ```bash
   cp /Volumes/EFI_Backup/config.plist /Volumes/EFI/EFI/OC/
   ```
4. **Reboot and test**

**BIOS Reset** (if needed):
1. **Power off** completely
2. **Remove CMOS battery** for 30 seconds
3. **Or use CLRTC jumper** on motherboard
4. **Reconfigure BIOS** settings (see BIOS_SETUP.md)

## 🔧 Boot Issues

### Kernel Panic on Boot
**Common Causes**:
- Incompatible kexts
- Wrong SMBIOS configuration
- Missing ACPI patches
- Hardware compatibility issues

**Diagnostic Steps**:
1. **Boot with verbose mode**: Add `-v` to boot-args
2. **Identify panic location**: Note where boot stops
3. **Check kext loading order**: <PERSON><PERSON> must load first
4. **Verify ACPI files**: Ensure all required SSDTs present

**Solutions**:
- **Kext issues**: Disable problematic kexts one by one
- **SMBIOS issues**: Regenerate with GenSMBIOS
- **ACPI issues**: Verify SSDT files are correct for Z390-E

### Black Screen After Apple Logo
**Symptoms**: System appears to boot but screen goes black

**Causes**:
- Graphics configuration issues
- WhateverGreen problems
- Display connection issues

**Solutions**:
1. **Try different platform-id**:
   - Current: `0x3E9B0007` (with displays)
   - Alternative: `0x3E980003` (headless)
2. **Check display connections**: Try different ports/cables
3. **Disable WhateverGreen**: Temporarily to isolate issue
4. **Add boot arguments**: `-igfxvesa` for basic graphics

### Stuck at "IOConsoleUsers: gIOScreenLock..."
**Cause**: Graphics driver issues or USB problems

**Solutions**:
1. **Check USB mapping**: Ensure proper USB configuration
2. **Verify graphics setup**: Platform-id and framebuffer patches
3. **Try safe mode**: Boot with `-x` flag
4. **Reset NVRAM**: Add `-reset_nvram` to boot-args

## 🔊 Audio Issues

### No Audio Output
**Diagnostic Steps**:
1. **Check System Preferences** → Sound → Output
2. **Verify AppleALC loading**: Check in Hackintool
3. **Test different outputs**: Line out, headphones, HDMI

**Solutions by Layout ID**:
- **Layout 1** (default): `<data>AQAAAA==</data>`
- **Layout 7**: `<data>BwAAAA==</data>`
- **Layout 11**: `<data>CwAAAA==</data>`
- **Layout 15**: `<data>DwAAAA==</data>`

**Advanced Troubleshooting**:
1. **Check codec detection**:
   ```bash
   ioreg -rxn IOHDACodecDevice
   ```
2. **Verify device path**: Should be `PciRoot(0x0)/Pci(0x1f,0x3)`
3. **Try alcid boot argument**: Add `alcid=1` (or 7,11,15) to boot-args

### Audio Crackling or Distortion
**Causes**:
- Wrong layout ID
- Buffer size issues
- Sample rate conflicts

**Solutions**:
1. **Try different layout IDs** systematically
2. **Check sample rate**: Set to 48kHz in Audio MIDI Setup
3. **Restart audio service**:
   ```bash
   sudo killall coreaudiod
   ```

## 🌐 Network Issues

### Ethernet Not Working
**Symptoms**: No ethernet connection with Intel I219-V

**Diagnostic Steps**:
1. **Check IntelMausi loading**: Verify in System Information
2. **Test cable and switch**: Rule out hardware issues
3. **Check BIOS settings**: Ensure network adapter enabled

**Solutions**:
1. **Update IntelMausi**: Use latest version (1.0.8)
2. **Check device path**: Verify ethernet controller detection
3. **Reset network settings**: Delete network preferences

### WiFi Not Connecting (FENVi T919)
**Symptoms**: WiFi card not detected or won't connect

**Diagnostic Steps**:
1. **Check card installation**: Ensure properly seated
2. **Verify antennas**: Check antenna connections
3. **Check System Information**: Should show BCM94360CD

**Solutions**:
1. **Reseat WiFi card**: Power off and reinstall
2. **Check antenna placement**: Ensure good signal reception
3. **Reset network preferences**: Delete WiFi networks and re-add

### Bluetooth Issues
**Symptoms**: Bluetooth not working or devices won't pair

**Solutions**:
1. **Reset Bluetooth module**:
   ```bash
   sudo pkill bluetoothd
   ```
2. **Delete Bluetooth preferences**:
   ```bash
   sudo rm /Library/Preferences/com.apple.Bluetooth.plist
   ```
3. **Check USB mapping**: Bluetooth may use internal USB

## 💾 Storage Issues

### NVMe SSD Not Detected
**Symptoms**: Drive not showing in Disk Utility

**Diagnostic Steps**:
1. **Check BIOS**: Ensure M.2 slot enabled
2. **Verify installation**: Drive properly seated
3. **Test in another system**: Rule out drive failure

**Solutions**:
1. **Enable NVMeFix**: Ensure kext is loaded
2. **Check BIOS settings**: M.2 configuration and SATA mode
3. **Try different M.2 slot**: If motherboard has multiple

### Slow Storage Performance
**Symptoms**: Poor read/write speeds on NVMe

**Diagnostic Steps**:
1. **Run speed test**: Use Blackmagic Disk Speed Test
2. **Check TRIM support**: Verify enabled for SSD
3. **Monitor temperatures**: Check for thermal throttling

**Solutions**:
1. **Enable TRIM**:
   ```bash
   sudo trimforce enable
   ```
2. **Update NVMeFix**: Use latest version
3. **Check thermal management**: Ensure adequate cooling

## 🖱️ USB Issues

### USB Ports Not Working
**Symptoms**: Some or all USB ports non-functional

**Diagnostic Steps**:
1. **Check BIOS settings**: XHCI Hand-off enabled
2. **Verify USB mapping**: Check UTBMap.kext loading
3. **Test different devices**: Rule out device-specific issues

**Solutions**:
1. **Regenerate USB mapping**: Use USBToolBox tool
2. **Check port limits**: Ensure under 15 ports mapped
3. **Verify XhciPortLimit**: Should be false after mapping

### USB 3.0 Running at USB 2.0 Speed
**Symptoms**: USB 3.0 devices showing 480 Mbps instead of 5 Gbps

**Solutions**:
1. **Check port types**: Ensure Type 3 (USB3) in mapping
2. **Test cables**: Use known good USB 3.0 cables
3. **Verify device support**: Ensure device is USB 3.0 capable

### Sleep/Wake Issues with USB
**Symptoms**: System won't sleep or wake properly

**Solutions**:
1. **Complete USB mapping**: Essential for proper sleep/wake
2. **Disable XhciPortLimit**: Must be false for sleep to work
3. **Check USB devices**: Some devices prevent sleep

## 🎮 Graphics Issues

### No Hardware Acceleration
**Symptoms**: Poor graphics performance, no Metal support

**Diagnostic Steps**:
1. **Check platform-id**: Verify correct for UHD 630
2. **Test WhateverGreen**: Ensure kext loading properly
3. **Check System Information**: Graphics should show acceleration

**Solutions**:
1. **Verify platform-id**: Use `0x3E9B0007` for displays
2. **Check framebuffer patches**: Ensure proper configuration
3. **Update WhateverGreen**: Use latest version

### Display Issues
**Symptoms**: Wrong resolution, flickering, or no display

**Solutions**:
1. **Try different cables**: HDMI, DisplayPort, etc.
2. **Check display settings**: Resolution and refresh rate
3. **Test different ports**: Try various video outputs

## ⚡ Power Management Issues

### CPU Not Scaling Frequency
**Symptoms**: CPU stuck at base or max frequency

**Diagnostic Steps**:
1. **Check SSDT-PLUG**: Ensure loading properly
2. **Monitor CPU frequency**: Use Intel Power Gadget
3. **Verify BIOS settings**: SpeedStep and Turbo enabled

**Solutions**:
1. **Regenerate SSDT-PLUG**: Use latest version
2. **Check SMBIOS**: Ensure iMac19,1 for Coffee Lake
3. **Verify power management**: Check Energy Saver settings

### Sleep/Wake Not Working
**Symptoms**: System won't sleep or wakes immediately

**Solutions**:
1. **Complete USB mapping**: Critical for sleep functionality
2. **Check wake reasons**:
   ```bash
   pmset -g log | grep -i wake
   ```
3. **Disable wake sources**: Network wake, USB wake, etc.

## 🔍 Diagnostic Tools

### System Information Commands
```bash
# Check kext loading
kextstat | grep -E "(Lilu|VirtualSMC|WhateverGreen|AppleALC)"

# Check USB mapping
ioreg -p IOUSB -l -w 0

# Check graphics
system_profiler SPDisplaysDataType

# Check audio
system_profiler SPAudioDataType

# Check power management
pmset -g
```

### Log Analysis
```bash
# System logs
log show --predicate 'eventMessage contains "error"' --last 1h

# Kernel messages
dmesg | grep -i error

# Boot logs
log show --predicate 'process == "kernel"' --start "2024-01-01"
```

## 📞 Getting Help

### Before Asking for Help
1. **Search existing solutions**: Check forums and guides
2. **Gather system information**: Hardware specs, software versions
3. **Document the issue**: Exact symptoms and error messages
4. **Try basic troubleshooting**: Follow this guide first

### Useful Resources
- **Dortania Guides**: https://dortania.github.io/
- **r/hackintosh**: Reddit community
- **tonymacx86**: Forums and guides
- **InsanelyMac**: Community forums
- **OpenCore Documentation**: Official documentation

### Information to Provide
When seeking help, include:
- **Hardware specifications**: Exact motherboard, CPU, GPU, etc.
- **Software versions**: OpenCore, kext versions, macOS version
- **Config.plist**: Sanitized (remove serials)
- **Error messages**: Exact text or screenshots
- **What you've tried**: Previous troubleshooting steps

## 🛡️ Prevention

### Best Practices
1. **Always backup**: Working EFI before changes
2. **Test incrementally**: One change at a time
3. **Document changes**: Keep notes on modifications
4. **Stay updated**: But test updates carefully
5. **Use stable versions**: Avoid beta software for daily use

### Regular Maintenance
1. **Monitor logs**: Check for recurring errors
2. **Update gradually**: OpenCore and kexts quarterly
3. **Test functionality**: Verify all features periodically
4. **Backup regularly**: System and EFI configurations
