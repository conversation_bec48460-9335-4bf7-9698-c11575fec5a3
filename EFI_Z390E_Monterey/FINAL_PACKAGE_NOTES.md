# Final Package Notes & Important Information

## 🎯 Package Overview

This EFI package is specifically designed for:
- **Motherboard**: ASUS ROG Strix Z390-E Gaming
- **CPU**: Intel Core i7-9700K (Coffee Lake Refresh)
- **WiFi Card**: FENVi T919 (BCM94360CD)
- **Storage**: PNY CS2230 500GB M.2 NVMe SSD
- **RAM**: 16GB (2x8GB) DDR4 2666 MHz
- **Target OS**: macOS Monterey (12.x)

## ⚠️ CRITICAL: Before First Use

### 1. Generate Unique SMBIOS
**The current SMBIOS values are PLACEHOLDERS and MUST be replaced:**

```xml
<!-- THESE ARE INVALID - REPLACE BEFORE USE -->
<key>SystemSerialNumber</key>
<string>C02YX0YSJV3Q</string>
<key>MLB</key>
<string>C02047601CDPHCDAD</string>
<key>SystemUUID</key>
<string>AABBCCDD-EEFF-0011-2233-************</string>
<key>ROM</key>
<data>ESIzRFVm</data>
```

**Use GenSMBIOS to generate valid serials:**
1. Download: https://github.com/corpnewt/GenSMBIOS
2. Run GenSMBIOS tool
3. Select option 3: Generate SMBIOS
4. Enter: `iMac19,1`
5. Replace values in config.plist

### 2. Download Required Files
**This package contains ONLY the structure and configuration.**
**You MUST download the actual files:**

- **OpenCore files**: BOOTx64.efi, OpenCore.efi, OpenRuntime.efi
- **Drivers**: HfsPlus.efi
- **Kexts**: All .kext files listed in README_KEXTS.md
- **ACPI**: All .aml files listed in ACPI folder

**See DOWNLOAD_LINKS.md for direct download links.**

## 📁 Package Contents

### Documentation Files
- **README.md**: Main overview and quick start
- **INSTALLATION_GUIDE.md**: Complete installation workflow
- **BIOS_SETUP.md**: Detailed BIOS configuration
- **USB_MAPPING_GUIDE.md**: USB mapping instructions
- **Z390E_USB_LAYOUT.md**: Z390-E specific USB layout
- **POST_INSTALL_USB_WORKFLOW.md**: Step-by-step USB mapping
- **POST_INSTALL_CHECKLIST.md**: Optimization and verification
- **TROUBLESHOOTING.md**: Common issues and solutions
- **DOWNLOAD_LINKS.md**: All required file download links

### EFI Structure
```
EFI/
├── BOOT/
│   └── BOOTx64.efi (PLACEHOLDER - DOWNLOAD REQUIRED)
└── OC/
    ├── ACPI/ (4 SSDT files - DOWNLOAD REQUIRED)
    ├── Drivers/ (2 driver files - DOWNLOAD REQUIRED)
    ├── Kexts/ (9 kext files - DOWNLOAD REQUIRED)
    └── config.plist (CONFIGURED - SMBIOS NEEDS UPDATING)
```

## 🔧 Configuration Highlights

### Optimized for Z390-E + i7-9700K
- **SMBIOS**: iMac19,1 (optimal for Coffee Lake + Monterey)
- **CPU Power Management**: SSDT-PLUG for proper frequency scaling
- **Graphics**: Intel UHD 630 with platform-id 0x3E9B0007
- **Audio**: ALC1220 with layout-id 1 (test 7, 11, 15 if needed)
- **USB**: Pre-configured for USBToolBox mapping workflow

### Hardware-Specific Features
- **FENVi T919**: Native WiFi/Bluetooth support (no additional kexts)
- **Intel I219-V**: Ethernet support with IntelMausi
- **NVMe SSD**: Optimization with NVMeFix
- **Z390 Chipset**: All required ACPI patches included

## 📋 Installation Workflow Summary

### Phase 1: Preparation
1. **Download all required files** (see DOWNLOAD_LINKS.md)
2. **Generate unique SMBIOS** with GenSMBIOS
3. **Configure BIOS** (see BIOS_SETUP.md)
4. **Create USB installer** with macOS Monterey

### Phase 2: Installation
1. **Copy EFI to USB installer**
2. **Boot and install macOS**
3. **Complete initial setup**
4. **Copy EFI to internal drive**

### Phase 3: Post-Installation
1. **USB mapping** with USBToolBox (CRITICAL)
2. **Audio optimization** (test different layout IDs)
3. **System verification** and testing
4. **Performance optimization**

## ⚡ What Works Out of Box

### ✅ Fully Functional
- **WiFi/Bluetooth**: FENVi T919 (BCM94360CD) - Native support
- **Ethernet**: Intel I219-V with IntelMausi
- **Graphics**: Intel UHD 630 with hardware acceleration
- **Audio**: ALC1220 with AppleALC (may need layout adjustment)
- **NVMe Storage**: PNY CS2230 with NVMeFix optimization
- **Power Management**: CPU frequency scaling with SSDT-PLUG

### ⚠️ Requires Post-Install Setup
- **USB Mapping**: Must complete USBToolBox mapping for proper sleep/wake
- **Audio Layout**: May need to test different layout IDs (1, 7, 11, 15)
- **SMBIOS**: Must generate unique serials for Apple services

## 🚨 Known Issues & Limitations

### USB Port Limit
- **Initial**: XhciPortLimit=true for installation
- **Post-Install**: MUST complete USB mapping and set XhciPortLimit=false
- **Sleep/Wake**: Will NOT work properly without USB mapping

### Audio Layout Testing
- **Default**: layout-id=1 configured
- **Alternatives**: Test 7, 11, 15 if audio issues
- **Method**: Change layout-id in DeviceProperties section

### BIOS Requirements
- **VT-d**: MUST be disabled
- **Above 4G Decoding**: MUST be enabled
- **XHCI Hand-off**: MUST be enabled
- **CSM**: MUST be disabled

## 🛠️ Customization Options

### Graphics Configuration
**Current**: Platform-id 0x3E9B0007 (with display outputs)
**Alternative**: Platform-id 0x3E980003 (headless mode for discrete GPU)

### Audio Layouts for ALC1220
- **Layout 1**: `<data>AQAAAA==</data>` (default)
- **Layout 7**: `<data>BwAAAA==</data>`
- **Layout 11**: `<data>CwAAAA==</data>`
- **Layout 15**: `<data>DwAAAA==</data>`

### Boot Arguments
**Current**: `-v keepsyms=1 debug=0x100 alcid=1`
**Remove `-v`**: For non-verbose boot after setup complete

## 📊 Performance Expectations

### Typical Performance
- **Boot Time**: 20-40 seconds to desktop
- **Sleep/Wake**: 3-8 seconds (after USB mapping)
- **Graphics**: Hardware acceleration for video/UI
- **Audio**: Clear, distortion-free output
- **Network**: Gigabit ethernet, 802.11ac WiFi

### Benchmarks (Reference)
- **Geekbench 5**: ~1100 single-core, ~6500 multi-core
- **Cinebench R23**: ~1300 single-core, ~9500 multi-core
- **NVMe Speed**: 1500+ MB/s read/write (depends on drive)

## 🔄 Update Strategy

### Quarterly Updates (Recommended)
1. **Backup current working EFI**
2. **Update OpenCore** to latest stable
3. **Update kexts** to latest versions
4. **Test thoroughly** before daily use

### macOS Updates
1. **Check compatibility** before updating
2. **Update OpenCore/kexts** if needed
3. **Test on non-critical system** first
4. **Keep working EFI backup** for rollback

## 🆘 Support & Resources

### Official Documentation
- **Dortania OpenCore Guide**: https://dortania.github.io/OpenCore-Install-Guide/
- **OpenCore Documentation**: https://github.com/acidanthera/OpenCorePkg/tree/master/Docs

### Community Support
- **r/hackintosh**: Reddit community
- **Dortania Discord**: Real-time support
- **InsanelyMac Forums**: Community discussions

### Troubleshooting
1. **Check TROUBLESHOOTING.md** for common issues
2. **Search existing solutions** before asking for help
3. **Provide detailed information** when seeking support

## ✅ Success Criteria

Your installation is successful when:
- [ ] **System boots** reliably without USB installer
- [ ] **All hardware functional** (audio, network, graphics, USB)
- [ ] **Sleep/wake working** (after USB mapping)
- [ ] **Performance acceptable** for daily use
- [ ] **System stable** for extended periods

## 🎉 Final Notes

This EFI package represents the latest best practices for Z390-E Hackintosh builds as of January 2025. The configuration is based on:

- **Dortania's OpenCore Install Guide**
- **Latest stable software versions**
- **Community-tested configurations**
- **Hardware-specific optimizations**

**Remember**: Hackintosh is a hobby that requires patience, learning, and community support. Take your time, follow the guides carefully, and don't hesitate to ask for help when needed.

**Good luck with your Z390-E Hackintosh build!** 🚀
