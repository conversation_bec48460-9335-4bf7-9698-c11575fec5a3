# Required Kexts for Z390-E + i7-9700K + macOS Monterey

## Download Links (Latest Versions as of January 2025)

### Essential Kexts
1. **Lilu.kext** (v1.7.1)
   - Download: https://github.com/acidanthera/Lilu/releases/tag/1.7.1
   - Purpose: Patching platform for other kexts

2. **VirtualSMC.kext** (v1.3.7)
   - Download: https://github.com/acidanthera/VirtualSMC/releases/tag/1.3.7
   - Purpose: SMC emulation
   - Also includes: SMCProcessor.kext, SMCSuperIO.kext

3. **WhateverGreen.kext** (v1.7.0)
   - Download: https://github.com/acidanthera/WhateverGreen/releases/tag/1.7.0
   - Purpose: Graphics patching for UHD 630

4. **AppleALC.kext** (v1.9.5)
   - Download: https://github.com/acidanthera/AppleALC/releases/tag/1.9.5
   - Purpose: Audio support for ALC1220 codec

5. **IntelMausi.kext** (v1.0.8)
   - Download: https://github.com/acidanthera/IntelMausi/releases/tag/1.0.8
   - Purpose: Intel I219-V ethernet support

6. **NVMeFix.kext** (v1.1.3)
   - Download: https://github.com/acidanthera/NVMeFix/releases/tag/1.1.3
   - Purpose: NVMe SSD optimization

### USB Mapping (Post-Installation)
7. **USBToolBox.kext** (v0.2)
   - Download: https://github.com/USBToolBox/kext/releases
   - Purpose: USB mapping framework

8. **UTBMap.kext** (Generated post-install)
   - Generated using USBToolBox tool
   - Purpose: Custom USB port mapping for Z390-E

## Installation Order
Place kexts in EFI/OC/Kexts/ folder in this order:
1. Lilu.kext
2. VirtualSMC.kext
3. SMCProcessor.kext
4. SMCSuperIO.kext
5. WhateverGreen.kext
6. AppleALC.kext
7. IntelMausi.kext
8. NVMeFix.kext
9. USBToolBox.kext
10. UTBMap.kext (after USB mapping)

## Notes
- FENVi T919 (BCM94360CD) requires NO additional kexts - native support
- All kexts should be the latest versions listed above
- UTBMap.kext will be generated after installation using USBToolBox
