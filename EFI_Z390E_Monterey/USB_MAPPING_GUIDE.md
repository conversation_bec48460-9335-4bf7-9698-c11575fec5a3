# USB Mapping Guide for ASUS ROG Strix Z390-E Gaming

## Overview
USB mapping is crucial for proper sleep/wake functionality and USB port recognition in macOS. The Z390-E has more than 15 USB ports, which exceeds macOS's limit, requiring custom mapping.

## Prerequisites
- macOS Monterey successfully installed and booting
- EFI configured with USBToolBox.kext enabled
- XhciPortLimit=true in config.plist (temporary for mapping)

## Method 1: USBToolBox Tool (Recommended)

### Step 1: Download USBToolBox Tool
1. Download the latest USBToolBox tool from: https://github.com/USBToolBox/tool/releases/tag/0.2
2. Choose the appropriate version:
   - **Windows**: `Windows.exe`
   - **macOS**: `macOS` (run in Terminal)
   - **Linux**: `Linux` (run in Terminal)

### Step 2: Run USBToolBox Tool
1. **On Windows** (recommended for easier port identification):
   ```cmd
   Windows.exe
   ```

2. **On macOS**:
   ```bash
   chmod +x macOS
   ./macOS
   ```

### Step 3: Discover Ports
1. Select option `D` - Discover Ports
2. **Test each physical USB port** on your Z390-E:
   - Insert a USB device (flash drive, mouse, keyboard)
   - Note which port number appears in the tool
   - Test both USB 2.0 and USB 3.0 devices in each port
   - Document the port layout

### Step 4: Z390-E Port Layout Reference
Based on typical Z390-E layout:

**Rear I/O Panel** (from left to right):
- **Port 1-2**: USB 2.0 (black)
- **Port 3-4**: USB 3.0 (blue) 
- **Port 5-6**: USB 3.0 (blue)
- **Port 7**: USB 3.1 Gen2 Type-A (red)
- **Port 8**: USB 3.1 Gen2 Type-C

**Internal Headers**:
- **Port 9-10**: USB 2.0 header (for case front panel)
- **Port 11-12**: USB 3.0 header (for case front panel)
- **Port 13-14**: Additional USB 2.0 header
- **Port 15**: USB 3.1 Gen2 Type-C header

### Step 5: Select Ports (15 Port Limit)
1. In USBToolBox tool, select option `S` - Select Ports
2. **Recommended port selection for Z390-E**:
   - **Keep**: All rear I/O ports (1-8)
   - **Keep**: Front panel USB 2.0 (9-10)
   - **Keep**: Front panel USB 3.0 (11-12)
   - **Keep**: One additional USB 2.0 header (13)
   - **Keep**: USB 3.1 Type-C header (15)
   - **Total**: 14 ports (within 15 limit)

3. **Port Types to assign**:
   - **USB 2.0 ports**: Type 0 (USB2)
   - **USB 3.0 ports**: Type 3 (USB3)
   - **USB 3.1 Gen2 Type-A**: Type 3 (USB3)
   - **USB 3.1 Gen2 Type-C**: Type 9 (TypeC+Sw) or Type 10 (TypeC)

### Step 6: Generate UTBMap.kext
1. Select option `K` - Build UTBMap.kext
2. The tool will generate `UTBMap.kext`
3. Copy this file to your EFI: `EFI/OC/Kexts/UTBMap.kext`

### Step 7: Update config.plist
1. **Enable UTBMap.kext**:
   - In config.plist → Kernel → Add
   - Find UTBMap.kext entry
   - Set `Enabled` to `true`

2. **Disable XhciPortLimit**:
   - In config.plist → Kernel → Quirks
   - Set `XhciPortLimit` to `false`

3. **Save config.plist** and reboot

## Method 2: Manual Mapping (Advanced)

### Step 1: Identify Controller Addresses
Use IORegistryExplorer to find USB controller addresses:
- **XHCI Controller**: Usually `PciRoot(0x0)/Pci(0x14,0x0)`
- **Additional Controllers**: May exist on Z390-E

### Step 2: Create Custom SSDT
Create SSDT-UIAC.aml with port definitions for your specific needs.

## Verification

### Test USB Functionality
After applying USB mapping:
1. **Test all mapped ports** with different devices
2. **Verify sleep/wake** works properly
3. **Check USB 3.0 speeds** with USB 3.0 devices
4. **Test Type-C functionality** if applicable

### Troubleshooting
- **Ports not working**: Check port numbers in USBToolBox
- **Sleep issues persist**: Verify XhciPortLimit=false
- **USB 3.0 slow**: Check port types (should be Type 3)
- **Type-C issues**: Try Type 9 vs Type 10

## Z390-E Specific Notes

### Known Issues
- **Bluetooth may use internal USB**: Don't disable if using WiFi card with BT
- **BIOS USB settings**: Ensure XHCI Hand-off is enabled
- **Legacy USB**: Can be disabled in BIOS after mapping

### Recommended BIOS Settings
- **XHCI Hand-off**: Enabled
- **Legacy USB Support**: Disabled (after mapping)
- **USB Mass Storage Driver Support**: Enabled

## Final Steps
1. **Backup working EFI** after successful USB mapping
2. **Document your port layout** for future reference
3. **Test thoroughly** before considering complete

## Additional Resources
- **USBToolBox Documentation**: https://github.com/USBToolBox/tool
- **Dortania USB Guide**: https://dortania.github.io/OpenCore-Post-Install/usb/
- **OpenCore Documentation**: https://dortania.github.io/docs/latest/Configuration.html
