# Complete Installation Guide - macOS Monterey on Z390-E

## Overview
This guide covers the complete installation process for macOS Monterey on ASUS ROG Strix Z390-E with Intel i7-9700K.

## Prerequisites
- [ ] ASUS ROG Strix Z390-E Gaming motherboard
- [ ] Intel i7-9700K CPU
- [ ] 16GB+ RAM
- [ ] 500GB+ storage drive
- [ ] FENVi T919 WiFi card (or compatible)
- [ ] 16GB+ USB drive for installer
- [ ] Access to a Mac or macOS VM for preparation

## Phase 1: Pre-Installation Setup

### Step 1: Download macOS Monterey
**Option A: From Mac App Store**
1. Open Mac App Store
2. Search for "macOS Monterey"
3. Download (may need to use older Mac)

**Option B: Direct Download**
1. Use gibMacOS tool: https://github.com/corpnewt/gibMacOS
2. Download macOS Monterey 12.x
3. Verify download integrity

### Step 2: Create Installation USB
**Using createinstallmedia (Recommended)**:
```bash
sudo /Applications/Install\ macOS\ Monterey.app/Contents/Resources/createinstallmedia --volume /Volumes/MyVolume
```

**Using gibMacOS**:
1. Run MakeInstall.bat (Windows) or MakeInstall.command (macOS)
2. Select downloaded Monterey installer
3. Select target USB drive

### Step 3: Prepare EFI Files
1. **Download all required files** (see README.md for links)
2. **Organize EFI structure**:
   ```
   EFI/
   ├── BOOT/
   │   └── BOOTx64.efi
   └── OC/
       ├── ACPI/
       ├── Drivers/
       ├── Kexts/
       ├── Tools/
       └── config.plist
   ```
3. **Generate unique SMBIOS** using GenSMBIOS
4. **Copy EFI folder** to USB installer

### Step 4: BIOS Configuration
Follow the detailed BIOS setup guide (BIOS_SETUP.md):
- [ ] Disable VT-d
- [ ] Enable Above 4G Decoding
- [ ] Disable CSM
- [ ] Disable Secure Boot
- [ ] Enable XHCI Hand-off
- [ ] Set SATA to AHCI mode

## Phase 2: Installation Process

### Step 5: Boot from USB Installer
1. **Insert USB installer** into rear USB port
2. **Power on system** and press F8 for boot menu
3. **Select USB drive** from boot options
4. **OpenCore boot menu** should appear
5. **Select "Install macOS Monterey"**

### Step 6: Disk Utility Setup
1. **Open Disk Utility** from macOS Utilities
2. **Show all devices** (View → Show All Devices)
3. **Select target drive** (your NVMe SSD)
4. **Erase drive**:
   - Name: Macintosh HD
   - Format: APFS
   - Scheme: GUID Partition Map
5. **Click Erase** and confirm

### Step 7: Install macOS
1. **Close Disk Utility** and select "Install macOS Monterey"
2. **Accept license agreement**
3. **Select "Macintosh HD"** as destination
4. **Installation will begin** (30-60 minutes)
5. **System will reboot** multiple times automatically

### Step 8: First Boot Setup
1. **Boot from USB** again after installation
2. **Select "Macintosh HD"** from OpenCore menu
3. **Complete macOS setup**:
   - Language and region
   - Network setup (WiFi should work with FENVi T919)
   - User account creation
   - Apple ID (optional)

## Phase 3: Post-Installation Configuration

### Step 9: Copy EFI to Internal Drive
1. **Mount EFI partition** of internal drive:
   ```bash
   sudo diskutil list
   sudo diskutil mount disk0s1
   ```
2. **Copy EFI folder** from USB to internal EFI partition:
   ```bash
   cp -R /Volumes/EFI_USB/EFI /Volumes/EFI/
   ```
3. **Verify copy** was successful
4. **Test boot** without USB installer

### Step 10: USB Mapping (Critical)
Follow the detailed USB mapping guide (USB_MAPPING_GUIDE.md):
1. **Download USBToolBox** tool
2. **Discover USB ports** systematically
3. **Select optimal 15 ports** for Z390-E
4. **Generate UTBMap.kext**
5. **Update config.plist** to enable UTBMap and disable XhciPortLimit
6. **Test sleep/wake functionality**

### Step 11: Audio Configuration
1. **Test current audio** (layout-id=1 by default)
2. **If audio doesn't work**, try alternative layouts:
   - layout-id=7: `<data>BwAAAA==</data>`
   - layout-id=11: `<data>CwAAAA==</data>`
   - layout-id=15: `<data>DwAAAA==</data>`
3. **Update config.plist** DeviceProperties section
4. **Reboot and test** each layout

### Step 12: Graphics Optimization
1. **Verify hardware acceleration**:
   ```bash
   system_profiler SPDisplaysDataType
   ```
2. **Check for Metal support** in About This Mac
3. **If using discrete GPU**, consider headless iGPU mode:
   - Change AAPL,ig-platform-id to `<data>AwCYPg==</data>` (0x3E980003)

## Phase 4: System Optimization

### Step 13: Power Management Verification
1. **Check CPU power states**:
   ```bash
   pmset -g
   ```
2. **Verify sleep/wake** functionality
3. **Test hibernation** if needed
4. **Monitor temperatures** with HWiNFO or similar

### Step 14: Network Configuration
1. **Test ethernet** (Intel I219-V with IntelMausi)
2. **Verify WiFi** (FENVi T919 should work natively)
3. **Test Bluetooth** functionality
4. **Configure network preferences**

### Step 15: Additional Software
Install essential macOS software:
- **Xcode Command Line Tools**: `xcode-select --install`
- **Homebrew**: Package manager for additional tools
- **ProperTree**: For config.plist editing
- **Hackintool**: System information and patching
- **HWiNFO**: Hardware monitoring

## Phase 5: Verification and Testing

### Step 16: Comprehensive Testing
- [ ] **Boot**: System boots reliably without USB
- [ ] **Sleep/Wake**: Works properly with USB mapping
- [ ] **Audio**: All outputs functional with correct layout
- [ ] **Graphics**: Hardware acceleration enabled
- [ ] **Network**: Ethernet and WiFi working
- [ ] **Bluetooth**: Pairing and connectivity working
- [ ] **USB**: All mapped ports functional at correct speeds
- [ ] **Storage**: NVMe SSD performing optimally

### Step 17: Performance Benchmarks
Run basic performance tests:
- **Geekbench 5**: CPU performance
- **Blackmagic Disk Speed Test**: Storage performance
- **Cinebench**: CPU rendering performance
- **GPU benchmarks**: If using discrete graphics

### Step 18: System Updates
1. **Check for macOS updates** in System Preferences
2. **Test update compatibility** with current EFI
3. **Backup working EFI** before major updates
4. **Update OpenCore** if needed for newer macOS versions

## Troubleshooting Common Issues

### Boot Issues
- **Kernel panic**: Check kext compatibility and loading order
- **Black screen**: Verify graphics configuration
- **USB not working**: Check BIOS USB settings and kext loading

### Performance Issues
- **Slow boot**: Optimize config.plist settings
- **Poor graphics**: Verify WhateverGreen configuration
- **Thermal throttling**: Check CPU power management

### Hardware Issues
- **Audio not working**: Try different AppleALC layout IDs
- **Ethernet issues**: Verify IntelMausi kext version
- **WiFi problems**: Check FENVi T919 installation

## Maintenance and Updates

### Regular Maintenance
- **Monthly**: Check for macOS updates
- **Quarterly**: Update OpenCore and kexts
- **As needed**: Backup working EFI configuration

### Update Process
1. **Backup current EFI**
2. **Test updates** on non-critical system first
3. **Update incrementally** (one component at a time)
4. **Verify functionality** after each update

## Success Criteria
✅ **System boots reliably** without USB installer
✅ **All hardware functional** (audio, network, graphics, USB)
✅ **Sleep/wake working** properly
✅ **Performance comparable** to native Mac
✅ **System stable** under normal workloads
✅ **Updates possible** with proper precautions

## Final Notes
- **Keep USB installer** for emergency recovery
- **Document your configuration** for future reference
- **Join community forums** for ongoing support
- **Contribute back** to the Hackintosh community

## Estimated Timeline
- **Preparation**: 2-3 hours
- **Installation**: 1-2 hours
- **Post-installation**: 2-4 hours
- **Testing and optimization**: 1-2 hours
- **Total**: 6-11 hours (spread over multiple sessions)
