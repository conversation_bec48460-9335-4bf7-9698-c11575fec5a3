# Download Links - All Required Files

## 🔗 Direct Download Links (Latest Versions)

### OpenCore Bootloader
**OpenCore 1.0.5** (July 7, 2024)
- **Download**: https://github.com/acidanthera/OpenCorePkg/releases/tag/1.0.5
- **File**: `OpenCore-1.0.5-RELEASE.zip`
- **Extract**: `BOOTx64.efi`, `OpenCore.efi`, `OpenRuntime.efi`

### Essential Drivers
**HfsPlus.efi** (HFS+ file system support)
- **Download**: https://github.com/acidanthera/OcBinaryData/blob/master/Drivers/HfsPlus.efi
- **Right-click**: "Save link as..." to download

### Required Kexts

#### 1. Lilu (Patching Platform)
**Lilu 1.7.1** (July 7, 2024)
- **Download**: https://github.com/acidanthera/Lilu/releases/tag/1.7.1
- **File**: `Lilu-1.7.1-RELEASE.zip`

#### 2. VirtualSMC (SMC Emulation)
**VirtualSMC 1.3.7** (July 7, 2024)
- **Download**: https://github.com/acidanthera/VirtualSMC/releases/tag/1.3.7
- **File**: `VirtualSMC-1.3.7-RELEASE.zip`
- **Includes**: `VirtualSMC.kext`, `SMCProcessor.kext`, `SMCSuperIO.kext`

#### 3. WhateverGreen (Graphics Patching)
**WhateverGreen 1.7.0** (July 7, 2024)
- **Download**: https://github.com/acidanthera/WhateverGreen/releases/tag/1.7.0
- **File**: `WhateverGreen-1.7.0-RELEASE.zip`

#### 4. AppleALC (Audio Support)
**AppleALC 1.9.5** (July 7, 2024)
- **Download**: https://github.com/acidanthera/AppleALC/releases/tag/1.9.5
- **File**: `AppleALC-1.9.5-RELEASE.zip`

#### 5. IntelMausi (Ethernet Support)
**IntelMausi 1.0.8** (December 3, 2024)
- **Download**: https://github.com/acidanthera/IntelMausi/releases/tag/1.0.8
- **File**: `IntelMausi-1.0.8-RELEASE.zip`

#### 6. NVMeFix (NVMe Optimization)
**NVMeFix 1.1.3** (July 7, 2024)
- **Download**: https://github.com/acidanthera/NVMeFix/releases/tag/1.1.3
- **File**: `NVMeFix-1.1.3-RELEASE.zip`

#### 7. USBToolBox (USB Mapping)
**USBToolBox Kext** (Latest)
- **Download**: https://github.com/USBToolBox/kext/releases
- **File**: `USBToolBox-1.1.1-RELEASE.zip`

### USB Mapping Tool
**USBToolBox Tool 0.2** (June 10, 2024)
- **Download**: https://github.com/USBToolBox/tool/releases/tag/0.2
- **Windows**: `Windows.exe`
- **macOS**: `macOS` binary
- **Linux**: `Linux` binary

### ACPI Files (Compiled)
**Dortania Pre-compiled SSDTs**
- **Base URL**: https://github.com/dortania/Getting-Started-With-ACPI/tree/master/extra-files/compiled
- **SSDT-PLUG.aml**: https://github.com/dortania/Getting-Started-With-ACPI/blob/master/extra-files/compiled/SSDT-PLUG.aml
- **SSDT-EC-USBX-DESKTOP.aml**: https://github.com/dortania/Getting-Started-With-ACPI/blob/master/extra-files/compiled/SSDT-EC-USBX-DESKTOP.aml
- **SSDT-AWAC.aml**: https://github.com/dortania/Getting-Started-With-ACPI/blob/master/extra-files/compiled/SSDT-AWAC.aml
- **SSDT-PMC.aml**: https://github.com/dortania/Getting-Started-With-ACPI/blob/master/extra-files/compiled/SSDT-PMC.aml

### Utility Tools

#### GenSMBIOS (SMBIOS Generation)
- **Download**: https://github.com/corpnewt/GenSMBIOS
- **Clone**: `git clone https://github.com/corpnewt/GenSMBIOS.git`
- **Run**: `GenSMBIOS.command` (macOS) or `GenSMBIOS.bat` (Windows)

#### ProperTree (Config.plist Editor)
- **Download**: https://github.com/corpnewt/ProperTree
- **Clone**: `git clone https://github.com/corpnewt/ProperTree.git`
- **Run**: `ProperTree.command` (macOS) or `ProperTree.bat` (Windows)

#### MountEFI (EFI Partition Mounting)
- **Download**: https://github.com/corpnewt/MountEFI
- **Clone**: `git clone https://github.com/corpnewt/MountEFI.git`

## 📋 Download Checklist

### Core OpenCore Files
- [ ] `OpenCore-1.0.5-RELEASE.zip` → Extract `BOOTx64.efi`, `OpenCore.efi`
- [ ] `OpenRuntime.efi` from OpenCore package
- [ ] `HfsPlus.efi` from OcBinaryData repository

### Kext Files
- [ ] `Lilu-1.7.1-RELEASE.zip`
- [ ] `VirtualSMC-1.3.7-RELEASE.zip` (includes SMCProcessor, SMCSuperIO)
- [ ] `WhateverGreen-1.7.0-RELEASE.zip`
- [ ] `AppleALC-1.9.5-RELEASE.zip`
- [ ] `IntelMausi-1.0.8-RELEASE.zip`
- [ ] `NVMeFix-1.1.3-RELEASE.zip`
- [ ] `USBToolBox-1.1.1-RELEASE.zip`

### ACPI Files
- [ ] `SSDT-PLUG.aml`
- [ ] `SSDT-EC-USBX-DESKTOP.aml` (rename to SSDT-EC-USBX.aml)
- [ ] `SSDT-AWAC.aml`
- [ ] `SSDT-PMC.aml`

### Tools
- [ ] `USBToolBox Tool 0.2` (for USB mapping)
- [ ] `GenSMBIOS` (for SMBIOS generation)
- [ ] `ProperTree` (for config.plist editing)

## 🔧 Installation Order

### 1. Create EFI Structure
```
EFI/
├── BOOT/
│   └── BOOTx64.efi
└── OC/
    ├── ACPI/
    │   ├── SSDT-PLUG.aml
    │   ├── SSDT-EC-USBX.aml
    │   ├── SSDT-AWAC.aml
    │   └── SSDT-PMC.aml
    ├── Drivers/
    │   ├── HfsPlus.efi
    │   └── OpenRuntime.efi
    ├── Kexts/
    │   ├── Lilu.kext
    │   ├── VirtualSMC.kext
    │   ├── SMCProcessor.kext
    │   ├── SMCSuperIO.kext
    │   ├── WhateverGreen.kext
    │   ├── AppleALC.kext
    │   ├── IntelMausi.kext
    │   ├── NVMeFix.kext
    │   ├── USBToolBox.kext
    │   └── UTBMap.kext (generate post-install)
    ├── Tools/
    └── config.plist
```

### 2. Generate SMBIOS
1. **Run GenSMBIOS**
2. **Select option 1**: Download MacSerial
3. **Select option 3**: Generate SMBIOS
4. **Enter**: `iMac19,1`
5. **Copy values** to config.plist

### 3. Configure config.plist
1. **Open with ProperTree**
2. **Update SMBIOS** values from GenSMBIOS
3. **Verify all kexts** are listed in Kernel → Add
4. **Check ACPI files** are listed in ACPI → Add
5. **Save and validate**

## ⚠️ Important Notes

### Version Compatibility
- **All versions listed** are latest as of January 2025
- **Check for updates** before downloading
- **Verify compatibility** with macOS Monterey

### File Integrity
- **Download from official sources** only
- **Verify checksums** if provided
- **Scan for malware** before use

### SMBIOS Security
- **Generate unique serials** with GenSMBIOS
- **Never use provided placeholder serials**
- **Keep serials private** and secure

### WiFi Card Note
- **FENVi T919 (BCM94360CD)** requires NO additional kexts
- **Native support** in macOS Monterey
- **No AirportBrcmFixup** or similar kexts needed

## 🆘 If Download Links Break

### Alternative Sources
1. **GitHub releases**: Always check official repositories
2. **Dortania builds**: https://dortania.github.io/builds/
3. **Community mirrors**: Check hackintosh forums

### Manual Building
If pre-compiled files unavailable:
1. **Clone source repositories**
2. **Build with Xcode** (requires macOS)
3. **Follow build instructions** in each repository

### Getting Help
- **r/hackintosh subreddit**: Community support
- **Dortania Discord**: Real-time help
- **GitHub issues**: Report broken links or bugs
