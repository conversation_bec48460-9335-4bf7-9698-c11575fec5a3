# Post-Installation Checklist & Optimization Guide

## Phase 1: Essential Post-Installation Tasks

### ✅ Critical Tasks (Complete First)
- [ ] **Copy EFI to internal drive** and verify boot without USB
- [ ] **Generate unique SMBIOS** with GenSMBIOS (replace placeholder serials)
- [ ] **Complete USB mapping** using USBToolBox (see USB_MAPPING_GUIDE.md)
- [ ] **Disable XhciPortLimit** after USB mapping is complete
- [ ] **Test sleep/wake functionality** thoroughly
- [ ] **Verify all hardware detection** in System Information

### ✅ Hardware Verification
- [ ] **CPU**: Intel i7-9700K detected correctly in About This Mac
- [ ] **RAM**: 16GB DDR4 showing correct speed and configuration
- [ ] **Storage**: NVMe SSD detected with TRIM support enabled
- [ ] **Graphics**: Intel UHD 630 with hardware acceleration
- [ ] **Audio**: ALC1220 working with selected layout ID
- [ ] **Ethernet**: Intel I219-V functional with IntelMausi
- [ ] **WiFi**: FENVi T919 (BCM94360CD) working natively
- [ ] **Bluetooth**: Pairing and connectivity functional

## Phase 2: System Optimization

### 🔧 Audio Optimization
**Current Layout**: layout-id=1 (default)

**Test Alternative Layouts if Issues**:
1. **Layout ID 7**:
   ```xml
   <key>layout-id</key>
   <data>BwAAAA==</data>
   ```
2. **Layout ID 11**:
   ```xml
   <key>layout-id</key>
   <data>CwAAAA==</data>
   ```
3. **Layout ID 15**:
   ```xml
   <key>layout-id</key>
   <data>DwAAAA==</data>
   ```

**Audio Testing Checklist**:
- [ ] **Line out** (rear green jack)
- [ ] **Headphone out** (front panel)
- [ ] **Microphone in** (rear pink jack)
- [ ] **Digital audio** (if using HDMI/DisplayPort)
- [ ] **System sounds** and alerts
- [ ] **Audio balance** and volume control

### 🎮 Graphics Optimization

**Current Configuration**: UHD 630 with platform-id 0x3E9B0007

**Verify Graphics Acceleration**:
```bash
# Check graphics info
system_profiler SPDisplaysDataType

# Verify Metal support
system_profiler SPDisplaysDataType | grep Metal
```

**Graphics Checklist**:
- [ ] **Hardware acceleration** enabled (check Metal support)
- [ ] **Display resolution** correct and stable
- [ ] **Multiple displays** working (if applicable)
- [ ] **Video playback** smooth in Safari/QuickTime
- [ ] **Graphics performance** acceptable for intended use

**Alternative Configurations**:
- **Headless mode** (if using discrete GPU):
  ```xml
  <key>AAPL,ig-platform-id</key>
  <data>AwCYPg==</data>
  ```

### ⚡ Power Management Optimization

**Verify CPU Power Management**:
```bash
# Check power management
pmset -g

# Monitor CPU frequency
sudo powermetrics -n 1 -i 1000 | grep CPU
```

**Power Management Checklist**:
- [ ] **CPU frequency scaling** working (check with monitoring tools)
- [ ] **Sleep/wake** functioning properly
- [ ] **Hibernation** working if enabled
- [ ] **Fan control** responsive to temperature
- [ ] **Power button** behavior correct
- [ ] **Energy Saver** settings optimized

### 🌐 Network Optimization

**Ethernet (Intel I219-V)**:
- [ ] **Connection speed** showing 1000baseT (Gigabit)
- [ ] **Wake on LAN** working if needed
- [ ] **Network stability** under load

**WiFi (FENVi T919 BCM94360CD)**:
- [ ] **5GHz networks** connecting properly
- [ ] **WiFi speeds** meeting expectations
- [ ] **Handoff/Continuity** working with other Apple devices
- [ ] **AirDrop** functionality
- [ ] **Personal Hotspot** working

**Bluetooth**:
- [ ] **Device pairing** successful
- [ ] **Audio devices** (headphones, speakers) working
- [ ] **Input devices** (mouse, keyboard) responsive
- [ ] **File transfer** via Bluetooth functional

## Phase 3: Advanced Optimization

### 🔒 Security and Privacy

**FileVault Encryption**:
- [ ] **Consider enabling** FileVault for disk encryption
- [ ] **Test boot process** with FileVault enabled
- [ ] **Backup recovery key** securely

**System Integrity Protection (SIP)**:
- [ ] **Current status**: Check with `csrutil status`
- [ ] **Modify if needed** for specific software requirements
- [ ] **Document changes** for future reference

### 📊 Performance Monitoring

**Install Monitoring Tools**:
- [ ] **HWiNFO64** (Windows) or **TG Pro** (macOS) for hardware monitoring
- [ ] **Activity Monitor** for system resource usage
- [ ] **Console** for system log monitoring

**Performance Benchmarks**:
- [ ] **Geekbench 5**: CPU performance baseline
- [ ] **Blackmagic Disk Speed Test**: Storage performance
- [ ] **Cinebench**: CPU rendering performance
- [ ] **GPU benchmarks**: Graphics performance (if applicable)

### 🛠️ System Maintenance

**Regular Maintenance Tasks**:
- [ ] **Weekly**: Check system logs for errors
- [ ] **Monthly**: Verify disk health and free space
- [ ] **Quarterly**: Update OpenCore and kexts
- [ ] **As needed**: Clean system caches and temporary files

**Backup Strategy**:
- [ ] **EFI backup**: Keep working EFI configuration saved
- [ ] **Time Machine**: Set up automated backups
- [ ] **Clone backup**: Create bootable system clone
- [ ] **Document configuration**: Keep notes on customizations

## Phase 4: Software Installation

### 🔧 Essential Development Tools

**Command Line Tools**:
```bash
# Install Xcode Command Line Tools
xcode-select --install

# Install Homebrew
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
```

**Hackintosh-Specific Tools**:
- [ ] **ProperTree**: Config.plist editor
- [ ] **Hackintool**: System information and patching
- [ ] **GenSMBIOS**: SMBIOS generation
- [ ] **MountEFI**: Easy EFI partition mounting
- [ ] **Kext Updater**: Keep kexts current

### 📱 macOS-Specific Features

**Apple Services**:
- [ ] **iCloud**: Sign in and sync (use valid SMBIOS)
- [ ] **App Store**: Download and install apps
- [ ] **FaceTime**: Test video calling
- [ ] **Messages**: SMS/iMessage functionality
- [ ] **AirDrop**: File sharing with other devices

**Continuity Features**:
- [ ] **Handoff**: Between Mac and iOS devices
- [ ] **Universal Clipboard**: Copy/paste across devices
- [ ] **AirPlay**: Screen mirroring to Apple TV
- [ ] **Sidecar**: Use iPad as second display (if supported)

## Phase 5: Troubleshooting and Fine-Tuning

### 🐛 Common Issues and Solutions

**Boot Issues**:
- **Slow boot**: Optimize config.plist, remove unnecessary kexts
- **Kernel panic**: Check kext compatibility and loading order
- **Black screen**: Verify graphics configuration

**Performance Issues**:
- **Thermal throttling**: Check CPU cooler, thermal paste
- **Slow storage**: Verify NVMe optimization with NVMeFix
- **Poor graphics**: Check WhateverGreen configuration

**Hardware Issues**:
- **USB not working**: Verify USB mapping and port limits
- **Audio crackling**: Try different AppleALC layout IDs
- **Network drops**: Check kext versions and compatibility

### 🔍 System Diagnostics

**Log Analysis**:
```bash
# Check system logs
log show --predicate 'eventMessage contains "error"' --last 1h

# Check kernel messages
dmesg | grep -i error

# Check USB mapping
ioreg -p IOUSB -l -w 0
```

**Hardware Diagnostics**:
- [ ] **Temperature monitoring**: CPU, GPU, system temps
- [ ] **Fan speed control**: Verify responsive fan curves
- [ ] **Memory test**: Run memory diagnostic if stability issues
- [ ] **Storage health**: Check SSD health and SMART data

## Phase 6: Future Maintenance

### 📅 Update Schedule

**Monthly Tasks**:
- [ ] **Check for macOS updates** (test compatibility first)
- [ ] **Review system logs** for recurring issues
- [ ] **Monitor hardware health** and temperatures

**Quarterly Tasks**:
- [ ] **Update OpenCore** to latest stable version
- [ ] **Update kexts** to latest compatible versions
- [ ] **Review and optimize** config.plist settings
- [ ] **Backup current configuration** before changes

**Annual Tasks**:
- [ ] **Full system backup** and restore test
- [ ] **Hardware maintenance** (dust cleaning, thermal paste)
- [ ] **Performance benchmarking** to track degradation
- [ ] **Security review** and updates

### 🎯 Optimization Goals

**Performance Targets**:
- [ ] **Boot time**: Under 30 seconds to desktop
- [ ] **Sleep/wake**: Under 5 seconds each direction
- [ ] **Application launch**: Comparable to native Mac
- [ ] **System responsiveness**: No noticeable lag in daily use

**Stability Targets**:
- [ ] **Uptime**: Multiple days without restart
- [ ] **No kernel panics** during normal operation
- [ ] **Consistent performance** under varying loads
- [ ] **Reliable hardware function** across all components

## Success Metrics

### ✅ System Health Indicators
- **CPU temperatures**: Under 80°C under load
- **System responsiveness**: No beach balls or freezing
- **Memory usage**: Efficient allocation without leaks
- **Storage performance**: Consistent read/write speeds
- **Network stability**: No dropped connections
- **Audio quality**: Clear, distortion-free output

### 📈 Performance Benchmarks
Document baseline performance for future comparison:
- **Geekbench 5 CPU**: Single/Multi-core scores
- **Disk Speed**: Sequential/Random read/write
- **Graphics**: Frame rates in standard tests
- **Boot time**: From power on to desktop
- **Sleep/wake**: Time measurements

## Final Verification

### 🎉 Completion Checklist
- [ ] **All hardware functional** and optimized
- [ ] **System stable** for extended periods
- [ ] **Performance acceptable** for intended use
- [ ] **Backup strategy** implemented and tested
- [ ] **Documentation complete** for future reference
- [ ] **Update plan** established for maintenance

**Congratulations!** Your Z390-E Hackintosh is now fully optimized and ready for daily use.
