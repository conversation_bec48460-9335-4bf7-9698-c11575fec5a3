# BIOS Setup Guide for ASUS ROG Strix Z390-E Gaming

## Prerequisites
- **BIOS Version**: Update to latest BIOS (recommended)
- **Default Settings**: Reset to defaults before making changes
- **Backup**: Save current BIOS settings profile before modifications

## Accessing BIOS
1. **Power on** the system
2. **Press DEL** repeatedly during boot to enter BIOS
3. **Alternative**: Press F2 during boot
4. **UEFI Mode**: Ensure system is in UEFI mode (not Legacy)

## BIOS Navigation
- **F7**: Switch between EZ Mode and Advanced Mode
- **F5**: Refresh/Reload settings
- **F9**: Load optimized defaults
- **F10**: Save and exit

## Required BIOS Settings

### 1. Main Tab
- **Fast Boot**: Disabled
- **CSM (Compatibility Support Module)**: Disabled
- **Secure Boot**: Disabled

### 2. Advanced Tab

#### CPU Configuration
- **Intel Virtualization Technology**: Enabled
- **Intel VT-d Technology**: **Disabled** (important for macOS)
- **CFG Lock**: **Disabled** (if available)
- **Intel SpeedStep**: Enabled
- **Intel Turbo Boost**: Enabled
- **Hyper-Threading**: Enabled

#### System Agent (SA) Configuration
- **VT-d**: **Disabled**
- **Above 4G Decoding**: **Enabled**
- **Re-Size BAR Support**: Disabled (for compatibility)

#### PCH Configuration
- **IOAPIC 24-119 Entries**: Enabled

#### Storage Configuration
- **SATA Mode**: **AHCI** (not RAID or IDE)

#### USB Configuration
- **Legacy USB Support**: Enabled (disable after USB mapping)
- **XHCI Hand-off**: **Enabled** (critical for USB)
- **USB Mass Storage Driver Support**: Enabled
- **USB 3.0 Support**: Enabled

#### Network Stack Configuration
- **Network Stack**: Disabled (optional)

### 3. Advanced → APM Configuration
- **Restore AC Power Loss**: Power Off
- **Power On By PCIe**: Disabled
- **Power On By RTC**: Disabled

### 4. Advanced → CPU Configuration → CPU Power Management
- **CPU C States**: Enabled
- **Enhanced C-states**: Enabled
- **C3 State Support**: Enabled
- **C6 State Support**: Enabled
- **C7 State Support**: Enabled
- **C8 State Support**: Enabled
- **Package C State Support**: Enabled
- **Long Duration Power Limit**: Auto
- **Long Duration Maintained**: Auto
- **Short Duration Power Limit**: Auto

### 5. Chipset Tab

#### System Agent (SA) Configuration
- **Graphics Configuration**:
  - **Primary Display**: Auto or PCIe (if using discrete GPU)
  - **iGPU Multi-Monitor**: Enabled (if using iGPU)
  - **DVMT Pre-Allocated**: 64MB (minimum for iGPU)
  - **DVMT Total Gfx Mem**: MAX

#### PCH Configuration
- **PCI Express Configuration**:
  - **PCIe Speed**: Auto
  - **ASPM**: Disabled (for stability)

### 6. Boot Tab
- **Fast Boot**: Disabled
- **Boot Option Priorities**: Set USB as first boot device for installation
- **CSM (Compatibility Support Module)**: **Disabled**
- **Secure Boot**: **Disabled**
- **OS Type**: Other OS (not Windows UEFI mode)

### 7. Security Tab
- **Secure Boot**: **Disabled**
- **Secure Boot Mode**: Custom (if available)

## Z390-E Specific Settings

### ROG-Specific Features
- **ROG GameFirst**: Disabled
- **ROG Keystone**: Disabled
- **Aura Sync**: Enabled (optional, for RGB control)

### Overclocking Settings (if applicable)
- **AI Overclock Tuner**: Auto or Manual
- **ASUS MultiCore Enhancement**: Disabled (for stability)
- **CPU Core Ratio**: Auto (unless manually overclocking)
- **Memory Frequency**: Set to your RAM's rated speed (2666MHz for your kit)

### Power Management
- **CPU Power Phase Control**: Auto
- **DRAM Power Phase Control**: Auto
- **ErP Ready**: Disabled
- **Restore AC Power Loss**: Power Off

## Critical Settings Summary

### ✅ Must Enable
- **Above 4G Decoding**: Enabled
- **XHCI Hand-off**: Enabled
- **SATA Mode**: AHCI
- **Intel Virtualization Technology**: Enabled

### ❌ Must Disable
- **VT-d**: Disabled
- **CSM**: Disabled
- **Secure Boot**: Disabled
- **Fast Boot**: Disabled
- **CFG Lock**: Disabled (if available)

### ⚠️ Important Notes
- **CFG Lock**: May not be visible in consumer BIOS
- **Above 4G Decoding**: Critical for proper PCIe device recognition
- **VT-d**: Must be disabled for macOS compatibility

## BIOS Update Process

### Before Updating
1. **Download latest BIOS** from ASUS support website
2. **Format USB drive** to FAT32
3. **Copy BIOS file** to USB root directory
4. **Rename file** to match ASUS requirements (if needed)

### Update Steps
1. **Enter BIOS** (DEL during boot)
2. **Go to Tool tab** → EZ Flash 3
3. **Select USB drive** and BIOS file
4. **Confirm update** and wait for completion
5. **Do NOT power off** during update process
6. **System will reboot** automatically when complete

### After Update
1. **Reset to defaults** (F9)
2. **Reconfigure all settings** as listed above
3. **Save and exit** (F10)

## Troubleshooting BIOS Issues

### System Won't Boot After Changes
1. **Clear CMOS**:
   - Power off system
   - Remove power cable
   - Remove CMOS battery for 30 seconds
   - Or use CLRTC jumper on motherboard
   - Restore power and reconfigure

### Can't Find Specific Setting
- **Update BIOS** to latest version
- **Check different tabs** (some settings move between versions)
- **Look in sub-menus** (settings may be nested)

### USB Devices Not Working
- **Enable XHCI Hand-off**
- **Enable Legacy USB Support** (temporarily)
- **Check USB 3.0 Support** is enabled

## Post-BIOS Configuration Checklist

After configuring BIOS:
- [ ] All critical settings applied
- [ ] BIOS settings saved
- [ ] System boots to USB installer
- [ ] USB devices functional
- [ ] No error messages during boot
- [ ] Ready for macOS installation

## BIOS Settings Export/Import

### Save Current Settings
1. **Go to Tool tab** → ASUS User Profile
2. **Save to USB** → Select profile slot
3. **Name your profile** (e.g., "macOS_Monterey")
4. **Save settings** to USB drive

### Load Saved Settings
1. **Go to Tool tab** → ASUS User Profile
2. **Load from USB** → Select saved profile
3. **Confirm load** and reboot

## Version-Specific Notes

### BIOS Version Differences
- **Older versions**: Some settings may be in different locations
- **Newer versions**: Additional options may be available
- **Beta versions**: Use with caution, may have stability issues

### Recommended BIOS Version
- **Latest stable release** from ASUS support
- **Avoid beta versions** for production use
- **Check compatibility** with your specific motherboard revision

## Final Verification

Before proceeding with macOS installation:
1. **Boot from USB installer** successfully
2. **All USB ports** functional for installation
3. **No BIOS error messages**
4. **System stable** with new settings
5. **Settings saved** and backed up

## Emergency Recovery

If system becomes unbootable:
1. **Clear CMOS** (remove battery or use jumper)
2. **Reset to defaults** (F9 in BIOS)
3. **Reconfigure essential settings** only
4. **Test boot** before applying all settings
5. **Apply settings gradually** to identify issues
