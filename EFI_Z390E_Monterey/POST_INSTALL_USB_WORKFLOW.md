# Post-Installation USB Mapping Workflow

## Prerequisites Checklist
- [ ] macOS Monterey successfully installed and booting
- [ ] EFI folder copied to internal drive
- [ ] USBToolBox.kext enabled in config.plist
- [ ] XhciPortLimit=true (temporary for mapping process)
- [ ] System stable and all basic functions working

## Phase 1: Preparation (5 minutes)

### Step 1: Download USBToolBox Tool
1. Visit: https://github.com/USBToolBox/tool/releases/tag/0.2
2. Download appropriate version:
   - **Windows**: `Windows.exe` (recommended for easier port identification)
   - **macOS**: `macOS` binary
   - **Linux**: `Linux` binary

### Step 2: Prepare Testing Materials
Gather these USB devices for testing:
- [ ] USB 2.0 flash drive or device
- [ ] USB 3.0 flash drive or device  
- [ ] USB mouse or keyboard
- [ ] USB-C device (if you have Type-C ports)
- [ ] Notepad for documenting port layout

### Step 3: Access Current EFI
1. Mount EFI partition:
   ```bash
   sudo diskutil mount disk0s1
   ```
2. Navigate to EFI folder: `/Volumes/EFI/EFI/OC/`
3. Backup current config.plist: `cp config.plist config.plist.backup`

## Phase 2: Port Discovery (15-20 minutes)

### Step 4: Run USBToolBox Tool

**On Windows (Recommended)**:
1. Run `Windows.exe` as Administrator
2. Select `D` - Discover Ports

**On macOS**:
1. Open Terminal
2. Navigate to download location
3. Run:
   ```bash
   chmod +x macOS
   sudo ./macOS
   ```
4. Select `D` - Discover Ports

### Step 5: Systematic Port Testing

**Test each physical port systematically**:

1. **Start with rear I/O panel** (left to right):
   - Insert USB device into first port
   - Note the port number that appears in USBToolBox
   - Record: "Port X = Rear I/O Position 1"
   - Remove device and move to next port
   - Repeat for all 8 rear ports

2. **Test front panel ports**:
   - Insert device into front left USB port
   - Record port number and position
   - Test front right USB port
   - Test any additional front ports

3. **Test with different device types**:
   - Use USB 2.0 device in each port
   - Use USB 3.0 device in same port
   - Note if port numbers change (they shouldn't)

### Step 6: Document Your Layout
Create a map like this:
```
Rear Panel (L→R): Port1, Port2, Port3, Port4, Port5, Port6, Port7, Port8
Front Panel: Port9(L), Port10(R), Port11(L-USB3), Port12(R-USB3)
Internal: Port13, Port14, Port15, Port16, Port17
```

## Phase 3: Port Selection (10 minutes)

### Step 7: Select Optimal 15 Ports
In USBToolBox tool:
1. Select `S` - Select Ports
2. **Recommended selection for Z390-E**:
   - ✅ Ports 1-8: All rear I/O ports
   - ✅ Ports 9-12: Front panel ports
   - ✅ Port 13: One internal USB 2.0
   - ✅ Port 15: One internal USB 3.0
   - ✅ Port 17: Internal Type-C (if present)
   - ❌ Ports 14, 16: Disabled to stay under 15 limit

### Step 8: Assign Port Types
For each selected port, assign correct type:
- **USB 2.0 ports**: Type 0 (USB2)
- **USB 3.0 ports**: Type 3 (USB3)  
- **USB 3.1 Type-A**: Type 3 (USB3)
- **USB 3.1 Type-C**: Type 9 (TypeC+Sw) or Type 10 (TypeC)

## Phase 4: Generate and Install Mapping (5 minutes)

### Step 9: Build UTBMap.kext
1. In USBToolBox tool, select `K` - Build UTBMap.kext
2. Tool will generate `UTBMap.kext` file
3. Note the location where it's saved

### Step 10: Install UTBMap.kext
1. Copy `UTBMap.kext` to: `/Volumes/EFI/EFI/OC/Kexts/UTBMap.kext`
2. Verify the kext is in the correct location

### Step 11: Update config.plist
1. Open config.plist with text editor or ProperTree
2. Navigate to: `Kernel` → `Add`
3. Find the UTBMap.kext entry
4. Change `Enabled` from `false` to `true`
5. Navigate to: `Kernel` → `Quirks`
6. Change `XhciPortLimit` from `true` to `false`
7. Save config.plist

## Phase 5: Testing and Verification (10 minutes)

### Step 12: Reboot and Test
1. Reboot the system
2. System should boot normally with new USB mapping

### Step 13: Verify Port Functionality
Test each mapped port:
- [ ] Insert USB device into each rear port
- [ ] Verify USB 3.0 ports achieve full speed
- [ ] Test front panel ports
- [ ] Check Type-C functionality (if applicable)

### Step 14: Sleep/Wake Test
1. Put system to sleep: `sudo pmset sleepnow`
2. Wake system with keyboard/mouse
3. Verify system wakes properly
4. Check that all USB devices still work after wake

## Phase 6: Troubleshooting (If Needed)

### Common Issues and Solutions

**Issue: System won't boot**
- Boot from USB installer
- Restore backup config.plist
- Set XhciPortLimit=true temporarily
- Regenerate USB mapping

**Issue: Some ports don't work**
- Check port selection in USBToolBox
- Verify port types are correct
- Re-test port discovery process

**Issue: USB 3.0 running slow**
- Ensure port type is set to Type 3 (USB3)
- Test with known good USB 3.0 device
- Check cable quality

**Issue: Sleep/wake problems**
- Verify XhciPortLimit=false
- Check for USB devices preventing sleep
- Review port mapping for errors

## Phase 7: Finalization (5 minutes)

### Step 15: Final Verification
- [ ] All essential USB ports working
- [ ] USB 3.0 achieving full speed
- [ ] Sleep/wake functioning properly
- [ ] No USB-related kernel panics
- [ ] Bluetooth still working (if using WiFi card)

### Step 16: Backup Working Configuration
1. Create backup of working EFI:
   ```bash
   cp -R /Volumes/EFI/EFI ~/Desktop/EFI_Working_USB_Mapped
   ```
2. Document your port mapping for future reference
3. Save USBToolBox output/configuration

### Step 17: Cleanup
1. Remove XhciPortLimit quirk references from notes
2. Update documentation with final port layout
3. Remove temporary files and tools

## Success Criteria
✅ **All mapped USB ports functional**
✅ **USB 3.0 devices achieve full speed (5Gbps+)**
✅ **Sleep/wake cycle works reliably**
✅ **No USB-related system instability**
✅ **Bluetooth functionality preserved**
✅ **System boots reliably with new mapping**

## Time Estimate
- **Total Time**: 45-60 minutes
- **Active Work**: 30-40 minutes
- **Testing/Verification**: 15-20 minutes

## Next Steps After USB Mapping
1. **Audio optimization**: Test different AppleALC layout IDs
2. **Graphics optimization**: Verify hardware acceleration
3. **Power management**: Check CPU power states
4. **Network optimization**: Test ethernet and WiFi performance
5. **System updates**: Verify update compatibility

## Emergency Recovery
If USB mapping causes boot issues:
1. Boot from USB installer with working EFI
2. Mount internal EFI partition
3. Restore backup config.plist
4. Set XhciPortLimit=true
5. Reboot and retry mapping process

## Documentation
Keep record of:
- Your specific port layout
- Selected port configuration
- Any custom modifications
- Working EFI backup location
