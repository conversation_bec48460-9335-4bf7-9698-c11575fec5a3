# ASUS ROG Strix Z390-E Gaming USB Port Layout

## Physical Port Layout

### Rear I/O Panel (Left to Right)
```
[PS/2] [USB2] [USB2] [USB3] [USB3] [USB3] [USB3] [USB3.1] [USB-C] [LAN] [Audio]
        ↓     ↓      ↓      ↓      ↓      ↓       ↓        ↓
       Port1 Port2  Port3  Port4  Port5  Port6   Port7   Port8
```

**Port Details**:
- **Port 1-2**: USB 2.0 (Black connectors)
- **Port 3-4**: USB 3.0 (Blue connectors)
- **Port 5-6**: USB 3.0 (Blue connectors) 
- **Port 7**: USB 3.1 Gen2 Type-A (Red connector)
- **Port 8**: USB 3.1 Gen2 Type-C

### Internal Headers

**USB 2.0 Headers**:
- **USB1**: 9-pin header (bottom of board) - Ports 9-10
- **USB2**: 9-pin header (near RAM) - Ports 13-14

**USB 3.0 Headers**:
- **USB3_1**: 19-pin header (bottom right) - Ports 11-12
- **USB3_2**: 19-pin header (top right) - Ports 15-16

**USB 3.1 Type-C Header**:
- **USB3.1_C**: Internal Type-C header - Port 17

## USB Controller Mapping

### Primary XHCI Controller (PciRoot(0x0)/Pci(0x14,0x0))

| Port | Physical Location | Type | Connector | Speed | Priority |
|------|------------------|------|-----------|-------|----------|
| 1    | Rear I/O #1      | USB2 | Type-A    | 480   | High     |
| 2    | Rear I/O #2      | USB2 | Type-A    | 480   | High     |
| 3    | Rear I/O #3      | USB3 | Type-A    | 5000  | High     |
| 4    | Rear I/O #4      | USB3 | Type-A    | 5000  | High     |
| 5    | Rear I/O #5      | USB3 | Type-A    | 5000  | High     |
| 6    | Rear I/O #6      | USB3 | Type-A    | 5000  | High     |
| 7    | Rear I/O #7      | USB3 | Type-A    | 10000 | High     |
| 8    | Rear I/O #8      | USB3 | Type-C    | 10000 | High     |
| 9    | Front Panel L    | USB2 | Type-A    | 480   | Medium   |
| 10   | Front Panel R    | USB2 | Type-A    | 480   | Medium   |
| 11   | Front Panel L    | USB3 | Type-A    | 5000  | Medium   |
| 12   | Front Panel R    | USB3 | Type-A    | 5000  | Medium   |
| 13   | Internal Header  | USB2 | Type-A    | 480   | Low      |
| 14   | Internal Header  | USB2 | Type-A    | 480   | Low      |
| 15   | Internal Header  | USB3 | Type-A    | 5000  | Low      |
| 16   | Internal Header  | USB3 | Type-A    | 5000  | Low      |
| 17   | Internal Type-C  | USB3 | Type-C    | 10000 | Medium   |

## Recommended 15-Port Mapping Strategy

### Priority-Based Selection
Given macOS's 15-port limit, prioritize based on usage:

**Essential Ports (8 ports)**:
- Ports 1-8: All rear I/O panel ports

**Front Panel (4 ports)**:
- Ports 9-12: Front panel USB 2.0 and 3.0

**Additional (3 ports)**:
- Port 13: One internal USB 2.0 header
- Port 15: One internal USB 3.0 header  
- Port 17: Internal Type-C header

**Total: 15 ports (at limit)**

### USBToolBox Configuration

```
Port 1:  Type 0 (USB2) - Rear USB 2.0 #1
Port 2:  Type 0 (USB2) - Rear USB 2.0 #2
Port 3:  Type 3 (USB3) - Rear USB 3.0 #1
Port 4:  Type 3 (USB3) - Rear USB 3.0 #2
Port 5:  Type 3 (USB3) - Rear USB 3.0 #3
Port 6:  Type 3 (USB3) - Rear USB 3.0 #4
Port 7:  Type 3 (USB3) - Rear USB 3.1 Type-A
Port 8:  Type 9 (TypeC+Sw) - Rear USB 3.1 Type-C
Port 9:  Type 0 (USB2) - Front Panel USB 2.0 Left
Port 10: Type 0 (USB2) - Front Panel USB 2.0 Right
Port 11: Type 3 (USB3) - Front Panel USB 3.0 Left
Port 12: Type 3 (USB3) - Front Panel USB 3.0 Right
Port 13: Type 0 (USB2) - Internal Header #1
Port 15: Type 3 (USB3) - Internal Header #1
Port 17: Type 10 (TypeC) - Internal Type-C Header
```

## Alternative Mapping Strategies

### Strategy 1: Maximum Rear I/O
If you primarily use rear ports:
- Keep all 8 rear ports
- Keep 4 front panel ports
- Keep 3 most useful internal ports

### Strategy 2: Internal Device Priority
If you have many internal USB devices:
- Keep all 8 rear ports
- Keep 2 front panel ports (USB 3.0 only)
- Keep 5 internal ports

### Strategy 3: Type-C Focus
If you heavily use Type-C:
- Keep all rear ports including Type-C
- Keep internal Type-C header
- Reduce internal USB-A headers

## Common Issues and Solutions

### Issue: Port Not Detected
**Solution**: 
- Verify physical connection
- Check BIOS USB settings
- Use different USB device for testing

### Issue: USB 3.0 Running at USB 2.0 Speed
**Solution**:
- Ensure port type is set to Type 3 (USB3)
- Check cable quality
- Verify device supports USB 3.0

### Issue: Type-C Not Working
**Solution**:
- Try Type 9 (TypeC+Sw) vs Type 10 (TypeC)
- Check orientation (Type-C is reversible)
- Verify Type-C device compatibility

### Issue: Sleep/Wake Problems
**Solution**:
- Ensure XhciPortLimit=false after mapping
- Verify all active ports are properly mapped
- Check for USB devices preventing sleep

## BIOS Configuration

### Required Settings
- **XHCI Hand-off**: Enabled
- **Legacy USB Support**: Auto or Disabled
- **USB Mass Storage Driver Support**: Enabled

### Optional Settings
- **USB 3.0 Support**: Enabled
- **USB 3.1 Support**: Enabled
- **USB Type-C Support**: Enabled

## Testing Checklist

After USB mapping implementation:

- [ ] All rear I/O ports functional
- [ ] Front panel ports working
- [ ] USB 3.0 devices achieve full speed
- [ ] Type-C ports functional (if mapped)
- [ ] Sleep/wake cycle works properly
- [ ] No USB-related kernel panics
- [ ] Bluetooth still functional (if using WiFi card)

## Notes for Z390-E Specific Quirks

1. **Bluetooth Internal USB**: FENVi T919 may use internal USB - don't disable
2. **ASUS USB Features**: Some ASUS-specific USB features may not work in macOS
3. **USB BIOS Flashback**: This feature uses dedicated circuitry, not affected by mapping
4. **ROG Connect**: USB debugging feature - disable in BIOS if not needed

## Backup and Recovery

1. **Always backup working EFI** before USB mapping changes
2. **Document your working configuration** for future reference
3. **Keep installation USB** with working EFI for recovery
4. **Test thoroughly** before finalizing configuration
